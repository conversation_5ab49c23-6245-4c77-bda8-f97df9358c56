-- ========================================================================
-- EXTRACT_FINAL_CSV - Migrado del pipeline_log_transacciones_duckdb.py
-- Extrae el CSV final TR-YYYYMMDD.csv
-- Replica exactamente la query LOG-TRANSACCIONES.sql
-- ========================================================================
-- CODE NINJA MASTER: Query exacta como LOG-TRANSACCIONES.sql

SELECT *
FROM read_parquet('{temp_dir}/{date_folder}/LOG_TRX_FINAL.parquet', union_by_name=true)
WHERE CAST("DateTime" AS DATE) = CAST('{fecha_inicio}' AS DATE)
ORDER BY "DateTime", "TransactionID"
