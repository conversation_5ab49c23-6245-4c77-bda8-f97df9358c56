-- ========================================================================
-- SP_PRE_LOG_TRX - COPIA EXACTA del pipeline original líneas 439-847
-- Procesa SP_PRE_LOG_TRX usando 100% tablas S3/Parquet
-- Arquitectura completamente S3 - NO usa Oracle
-- ========================================================================

WITH
TRX_HEADER AS (
    SELECT
        MTH.*,
        REPLACE(CAST(MTH.PAYEE_IDENTIFIER_VALUE AS VARCHAR),'1','') AS NEW_PAYEE_IDENTIFIER_VALUE,
        REPLACE(CAST(MTH.PAYER_IDENTIFIER_VALUE AS VARCHAR),'1','') AS NEW_PAYER_IDENTIFIER_VALUE
    FROM read_parquet('{mtx_transaction_header_path}', union_by_name=true) MTH
    LEFT JOIN read_parquet('{sys_service_types_path}', union_by_name=true) SST
        ON MTH.SERVICE_TYPE = SST.SERVICE_TYPE
    -- CODE NINJA: Ya no necesario filtrar por fecha - solo leemos archivos del día específico
    -- WHERE CAST(MTH.TRANSFER_DATE AS TIMESTAMP) >= CAST('{fecha_inicio}' AS TIMESTAMP)
    --     AND CAST(MTH.TRANSFER_DATE AS TIMESTAMP) < CAST('{fecha_inicio}' AS TIMESTAMP) + INTERVAL '1 day'
    WHERE 1=1
        AND MTH.TRANSFER_STATUS IN ('TA','TS')
        AND MTH.TRANSFER_VALUE <> 0
        AND SST.IS_FINANCIAL = 'Y'
        -- CODE NINJA MASTER: FILTRO EXPLÍCITO DE FECHA PARA RESPETAR ARGUMENTO
        -- Garantiza que solo se procesen registros del día especificado
        AND CAST(MTH.TRANSFER_DATE AS DATE) = CAST('{fecha_inicio}' AS DATE)
),
TRX_ITEMS AS (
    SELECT
        MTI.TRANSFER_ID, MTI.TRANSFER_VALUE, MTI.WALLET_NUMBER,
        MTI.SECOND_PARTY_WALLET_NUMBER, MTI.SERVICE_TYPE,
        MTI.ISSUER_ID, MTI.SECOND_PARTY_ISSUER_ID,
        MTI.SECOND_PARTY_MARKETING_PROFILE_CODE, MTI.TRANSACTION_TYPE
    FROM read_parquet('{mtx_transaction_items_path}', union_by_name=true) MTI
    INNER JOIN TRX_HEADER MTH ON MTI.TRANSFER_ID = MTH.TRANSFER_ID
),
MTX_WALLET_LATEST AS (
    SELECT
        USER_ID,
        WALLET_NUMBER,
        ROW_NUMBER() OVER (PARTITION BY USER_ID ORDER BY MODIFIED_ON DESC) as RN
    FROM read_parquet('{mtx_wallet_path}', union_by_name=true)
    WHERE USER_ID LIKE 'US.%' OR USER_ID = '945661'  -- US.xxxxx + caso especial 945661
),
MTX_WALLET_945661 AS (
    SELECT
        USER_ID,
        WALLET_NUMBER,
        STATUS,
        ROW_NUMBER() OVER (PARTITION BY USER_ID ORDER BY MODIFIED_ON DESC) as RN_DESC,
        ROW_NUMBER() OVER (PARTITION BY USER_ID ORDER BY MODIFIED_ON ASC) as RN_ASC
    FROM read_parquet('{mtx_wallet_path}', union_by_name=true)
    WHERE USER_ID = '945661'
),
-- CTE WALLETS: Replica EXACTAMENTE Oracle SP_PRE_LOG_USR líneas 12-20
WALLETS AS (
    SELECT
        MW.USER_ID,
        MW.WALLET_NUMBER,
        MW.ISSUER_ID,
        MW.USER_GRADE,
        -- LÓGICA EXACTA DE ORACLE: Solo MODIFIED_ON DESC, SIN filtro STATUS
        ROW_NUMBER() OVER (PARTITION BY MW.USER_ID ORDER BY MW.MODIFIED_ON DESC) AS ORDEN
    FROM read_parquet('{mtx_wallet_path}', union_by_name=true) MW
),
USER_DATA AS (
    SELECT DISTINCT
        UD.USER_ID,
        UD.O_USER_ID AS M_USER_ID,
        UD.PROFILE_TRX AS PROFILE,
        UD.WALLET_NUMBER AS ACCOUNT_ID,
        -- Aplicar lógica EXACTA de Oracle SP_PRE_LOG_USR líneas 33-37
        CASE
            WHEN UD.O_USER_ID LIKE 'US.%' THEN
                COALESCE(MW.WALLET_NUMBER, UD.WALLET_NUMBER)
            WHEN UD.O_USER_ID = '945661' THEN
                -- Caso especial: usar WALLET_NUMBER original para cálculos base
                UD.WALLET_NUMBER
            ELSE
                -- LÓGICA EXACTA ORACLE: ATTR8 o WALLET_NUMBER procesado
                CASE
                    WHEN UP.ATTR8 IS NOT NULL THEN UP.ATTR8
                    WHEN LENGTH(REPLACE(MW.WALLET_NUMBER,'UA.','')) > 15 THEN
                        SUBSTR(REPLACE(MW.WALLET_NUMBER,'UA.',''), -15)
                    ELSE REPLACE(MW.WALLET_NUMBER,'UA.','')
                END
        END AS WALLET_NUMBER,
        UD.MSISDN,
        UD.USER_CODE,
        UD.LOGIN_ID,
        UD.WORKSPACE_ID,
        UD.ISSUER_CODE,
        UD.ID_TYPE
    FROM read_parquet('{user_data_trx_path}', union_by_name=true) UD
    -- JOIN EXACTO de Oracle SP_PRE_LOG_USR línea 113: ORDEN=1 (más reciente)
    LEFT JOIN WALLETS MW ON UD.O_USER_ID = MW.USER_ID AND MW.ORDEN = 1
    -- JOIN con USER_PROFILE para obtener ATTR8 (lógica Oracle líneas 33-37)
    LEFT JOIN read_parquet('{user_profile_path}', union_by_name=true) UP
        ON UD.O_USER_ID = UP.USER_ID
),
MTI_SCP AS (
    SELECT MTI.TRANSFER_ID, MTI.TRANSFER_VALUE
    FROM TRX_ITEMS MTI
    WHERE MTI.TRANSACTION_TYPE = 'SCP'
),
WALLETS_GRADE AS (
    SELECT MW.ISSUER_ID, MW.WALLET_NUMBER, CG.GRADE_NAME
    FROM read_parquet('{mtx_wallet_path}', union_by_name=true) MW
    INNER JOIN read_parquet('{channel_grades_path}', union_by_name=true) CG
        ON MW.USER_GRADE = CG.GRADE_CODE
),
MTI_MP AS (
    SELECT
        MTI.TRANSFER_ID, MTI.WALLET_NUMBER, MTI.SECOND_PARTY_WALLET_NUMBER,
        MTI.ISSUER_ID, MTI.SECOND_PARTY_ISSUER_ID,
        WG.GRADE_NAME AS GRADE, WG2.GRADE_NAME AS SECOND_GRADE
    FROM TRX_ITEMS MTI
    LEFT JOIN WALLETS_GRADE WG ON MTI.WALLET_NUMBER = WG.WALLET_NUMBER
    LEFT JOIN WALLETS_GRADE WG2 ON MTI.SECOND_PARTY_WALLET_NUMBER = WG2.WALLET_NUMBER
    WHERE MTI.TRANSACTION_TYPE = 'MP'
),
MTI_MR AS (
    SELECT
        MTI.TRANSFER_ID, MTI.WALLET_NUMBER, MTI.ISSUER_ID,
        CASE
            WHEN MC.CATEGORY_NAME = 'Final User' THEN 'USUARIO FINAL'
            WHEN MC.CATEGORY_NAME = 'BIMER User' THEN 'BIMER'
        END AS PAYER_CATEGORY_CODE
    FROM TRX_ITEMS MTI
    INNER JOIN read_parquet('{marketing_profile_path}', union_by_name=true) MP
        ON MTI.SECOND_PARTY_MARKETING_PROFILE_CODE = MP.MARKETING_PROFILE_CODE
    INNER JOIN read_parquet('{mtx_categories_path}', union_by_name=true) MC
        ON MP.CATEGORY_CODE = MC.CATEGORY_CODE
    WHERE MTI.TRANSACTION_TYPE = 'MR'
        AND MTI.SERVICE_TYPE IN ('ISSUERMOV','CHANGECAT')
),
REVERSAL AS (
    SELECT MTH.TRANSFER_ID, MTH.FIELD7
    FROM TRX_HEADER MTH
),
TRX_DATA_DAY AS (
    SELECT
        MTH.FIELD7,
        MTH.TRANSFER_ID,
        MTH.FTXN_ID,
        MTH.SOURCE,
        CASE WHEN CAST(MTH.PAYER_USER_ID AS VARCHAR) = 'IND012' THEN CAST(MTH.PAYER_IDENTIFIER_VALUE AS VARCHAR)
             ELSE CAST(MTH.PAYER_USER_ID AS VARCHAR) END AS PAYER_USER_ID,
        CAST(MTH.PAYEE_USER_ID AS VARCHAR) AS PAYEE_USER_ID,
        MTH.NEW_PAYEE_IDENTIFIER_VALUE AS PAYEE_IDENTIFIER_VALUE,
        MTH.NEW_PAYER_IDENTIFIER_VALUE AS PAYER_IDENTIFIER_VALUE,
        CAST(MTH.CREATED_BY AS VARCHAR) AS CREATED_BY,
        CAST(MTH.MODIFIED_BY AS VARCHAR) AS MODIFIED_BY,
        MTH.TRANSFER_DATE,
        MTH.TRANSFER_STATUS,
        MTH.TRANSFER_VALUE,
        MSC.TRANSFER_VALUE AS FEE,
        MR.PAYER_CATEGORY_CODE,
        MTH.REQUEST_GATEWAY_TYPE AS CANAL,
        -- Lógica de REMARKS exacta como Oracle líneas 115-120
        CASE
            WHEN MTH.NEW_PAYEE_IDENTIFIER_VALUE IN ('claro','sentinel')
                THEN json_extract_string(CAST(MTH.PARTNER_DATA AS VARCHAR), '$.codigoPago')
            WHEN MTH.NEW_PAYEE_IDENTIFIER_VALUE = 'bitel'
                THEN SUBSTR(CAST(MTH.FIELD8 AS VARCHAR), 1, STRPOS(CAST(MTH.FIELD8 AS VARCHAR), '@') - 1)
            WHEN MTH.NEW_PAYEE_IDENTIFIER_VALUE = 'unique'
                THEN SUBSTR(CAST(MTH.REMARKS AS VARCHAR), 1, STRPOS(CAST(MTH.REMARKS AS VARCHAR), '_') - 1)
            ELSE CAST(MTH.REMARKS AS VARCHAR)
        END AS REMARKS,
        MTH.REQUEST_GATEWAY_TYPE,
        MP.WALLET_NUMBER AS PAYER_WALLET_NUMBER,
        CASE
            WHEN MTH.SERVICE_TYPE IN ('ISSUERMOV','CHANGECAT') THEN MR.WALLET_NUMBER
            ELSE MP.SECOND_PARTY_WALLET_NUMBER
        END AS PAYEE_WALLET_NUMBER,
        -- Aplicar lógica Oracle exacta para GRADE_NAME
        UPPER(MP.GRADE) AS PAYER_GRADE,
        UPPER(MP.SECOND_GRADE) AS PAYEE_GRADE,
        ID1.ISSUER_CODE AS PAYER_ISSUER_CODE,
        ID2.ISSUER_CODE AS PAYEE_ISSUER_CODE,
        MTH.PAYER_PROVIDER_ID,
        MTH.RECONCILIATION_BY,
        MTH.FIELD2,
        -- Lógica de TRX_SERVICE exacta como Oracle líneas 134-155
        CASE
            WHEN MTH.SERVICE_TYPE='CASHIN' AND MTH.MODIFIED_BY <> 'IND012' THEN 'CASH_IN'
            WHEN MTH.SERVICE_TYPE='CASHOUT' THEN 'CASH_OUT'
            WHEN MTH.SERVICE_TYPE='P2P' THEN 'TRANSFER'
            WHEN (MTH.SERVICE_TYPE='BILLPAY' AND (MTH.PAYEE_IDENTIFIER_VALUE = 'crandes' OR
                (MTH.PAYER_USER_ID IN {special_users_tuple} OR
                 MTH.PAYEE_USER_ID IN {special_users_tuple}))) THEN 'PAYMENT'
            WHEN (MTH.SERVICE_TYPE='BILLPAY' AND (MTH.PAYEE_IDENTIFIER_VALUE <> 'crandes' OR
                (MTH.PAYER_USER_ID NOT IN {special_users_tuple} AND
                 MTH.PAYEE_USER_ID NOT IN {special_users_tuple}))) THEN 'EXTERNAL_PAYMENT'
            WHEN MTH.SERVICE_TYPE='OFFUSOUT' THEN 'PAYMENT'
            WHEN MTH.SERVICE_TYPE IN ('STOCKCRT','STOCKINT','STOCK') THEN 'DEPOSIT'
            WHEN MTH.SERVICE_TYPE IN ('C2C','INVC2C') THEN 'FLOAT_TRANSFER'
            WHEN MTH.SERVICE_TYPE IN ('MULTIDRCR2') OR
                (MTH.SERVICE_TYPE = 'CASHIN' AND MTH.MODIFIED_BY = 'IND012') THEN 'BATCH_TRANSFER'
            WHEN MTH.SERVICE_TYPE IN ('OPTW') THEN 'TRANSFER_TO_ANY_BANK_ACCOUNT'
            WHEN MTH.SERVICE_TYPE IN ('STOCKTFR') THEN 'CUSTODY_ACCOUNTS_TRANSFER'
            WHEN MTH.SERVICE_TYPE IN ('ATMCASHOUT') THEN 'CASH_OUT_ATM'
            WHEN MTH.SERVICE_TYPE IN ('ISSUERMOV','CHANGECAT') THEN 'ADJUSTMENT'
            WHEN MTH.SERVICE_TYPE IN ('TXNCORRECT') AND MTH.ATTR_3_NAME = 'BULK_PAYMENT_BATCHID'
                THEN 'REVERSAL_BATCH_TRANSFER'
            WHEN MTH.SERVICE_TYPE IN ('TXNCORRECT') AND
                (MTH.ATTR_3_VALUE IN ('CASHOUT','CASHIN','ATMCASHOUT') OR MTH.ATTR_3_VALUE IS NULL)
                THEN 'REVERSAL'
            WHEN MTH.SERVICE_TYPE IN ('TXNCORRECT') AND MTH.ATTR_3_VALUE='BILLPAY' THEN
                CASE
                    WHEN (MTH.PAYEE_IDENTIFIER_VALUE = 'crandes' OR
                        (MTH.PAYER_USER_ID IN {special_users_tuple} OR
                         MTH.PAYEE_USER_ID IN {special_users_tuple})) THEN 'TRANSFER'
                    ELSE 'REFUND'
                END
            ELSE MTH.SERVICE_TYPE
        END AS TRX_SERVICE
    FROM TRX_HEADER MTH
    LEFT JOIN MTI_MP MP ON MTH.TRANSFER_ID = MP.TRANSFER_ID
    LEFT JOIN MTI_MR MR ON MTH.TRANSFER_ID = MR.TRANSFER_ID
    LEFT JOIN MTI_SCP MSC ON MTH.TRANSFER_ID = MSC.TRANSFER_ID
    LEFT JOIN read_parquet('{issuer_details_path}', union_by_name=true) ID1
        ON MP.ISSUER_ID = ID1.ISSUER_ID
    LEFT JOIN read_parquet('{issuer_details_path}', union_by_name=true) ID2
        ON MP.SECOND_PARTY_ISSUER_ID = ID2.ISSUER_ID
)
MTX_WALLET_LATEST AS (
    SELECT
        USER_ID,
        WALLET_NUMBER,
        ROW_NUMBER() OVER (PARTITION BY USER_ID ORDER BY MODIFIED_ON DESC) as RN
    FROM read_parquet('{mtx_wallet_path}', union_by_name=true)
    WHERE USER_ID LIKE 'US.%' OR USER_ID = '945661'  -- US.xxxxx + caso especial 945661
),
MTX_WALLET_945661 AS (
    SELECT
        USER_ID,
        WALLET_NUMBER,
        STATUS,
        ROW_NUMBER() OVER (PARTITION BY USER_ID ORDER BY MODIFIED_ON DESC) as RN_DESC,
        ROW_NUMBER() OVER (PARTITION BY USER_ID ORDER BY MODIFIED_ON ASC) as RN_ASC
    FROM read_parquet('{mtx_wallet_path}', union_by_name=true)
    WHERE USER_ID = '945661'
),
-- CTE WALLETS: Replica EXACTAMENTE Oracle SP_PRE_LOG_USR líneas 12-20
WALLETS AS (
    SELECT
        MW.USER_ID,
        MW.WALLET_NUMBER,
        MW.ISSUER_ID,
        MW.USER_GRADE,
        -- LÓGICA EXACTA DE ORACLE: Solo MODIFIED_ON DESC, SIN filtro STATUS
        ROW_NUMBER() OVER (PARTITION BY MW.USER_ID ORDER BY MW.MODIFIED_ON DESC) AS ORDEN
    FROM read_parquet('{mtx_wallet_path}', union_by_name=true) MW
),
USER_DATA AS (
    SELECT DISTINCT
        UD.USER_ID,
        UD.O_USER_ID AS M_USER_ID,
        UD.PROFILE_TRX AS PROFILE,
        UD.WALLET_NUMBER AS ACCOUNT_ID,
        -- Aplicar lógica EXACTA de Oracle SP_PRE_LOG_USR líneas 33-37
        CASE
            WHEN UD.O_USER_ID LIKE 'US.%' THEN
                COALESCE(MW.WALLET_NUMBER, UD.WALLET_NUMBER)
            WHEN UD.O_USER_ID = '945661' THEN
                -- Caso especial: usar WALLET_NUMBER original para cálculos base
                UD.WALLET_NUMBER
            ELSE
                -- LÓGICA EXACTA ORACLE: ATTR8 o WALLET_NUMBER procesado
                CASE
                    WHEN UP.ATTR8 IS NOT NULL THEN UP.ATTR8
                    WHEN LENGTH(REPLACE(MW.WALLET_NUMBER,'UA.','')) > 15 THEN
                        SUBSTR(REPLACE(MW.WALLET_NUMBER,'UA.',''), -15)
                    ELSE REPLACE(MW.WALLET_NUMBER,'UA.','')
                END
        END AS WALLET_NUMBER,
        UD.MSISDN,
        UD.USER_CODE,
        UD.LOGIN_ID,
        UD.WORKSPACE_ID,
        UD.ISSUER_CODE,
        UD.ID_TYPE
    FROM read_parquet('{user_data_trx_path}', union_by_name=true) UD
    -- JOIN EXACTO de Oracle SP_PRE_LOG_USR línea 113: ORDEN=1 (más reciente)
    LEFT JOIN WALLETS MW ON UD.O_USER_ID = MW.USER_ID AND MW.ORDEN = 1
    -- JOIN con USER_PROFILE para obtener ATTR8 (lógica Oracle líneas 33-37)
    LEFT JOIN read_parquet('{user_profile_path}', union_by_name=true) UP
        ON UD.O_USER_ID = UP.USER_ID
),
MTI_SCP AS (
    SELECT MTI.TRANSFER_ID, MTI.TRANSFER_VALUE
    FROM TRX_ITEMS MTI
    WHERE MTI.TRANSACTION_TYPE = 'SCP'
),
WALLETS_GRADE AS (
    SELECT MW.ISSUER_ID, MW.WALLET_NUMBER, CG.GRADE_NAME
    FROM read_parquet('{mtx_wallet_path}', union_by_name=true) MW
    INNER JOIN read_parquet('{channel_grades_path}', union_by_name=true) CG
        ON MW.USER_GRADE = CG.GRADE_CODE
),
MTI_MP AS (
    SELECT
        MTI.TRANSFER_ID, MTI.WALLET_NUMBER, MTI.SECOND_PARTY_WALLET_NUMBER,
        MTI.ISSUER_ID, MTI.SECOND_PARTY_ISSUER_ID,
        WG.GRADE_NAME AS GRADE, WG2.GRADE_NAME AS SECOND_GRADE
    FROM TRX_ITEMS MTI
    LEFT JOIN WALLETS_GRADE WG ON MTI.WALLET_NUMBER = WG.WALLET_NUMBER
    LEFT JOIN WALLETS_GRADE WG2 ON MTI.SECOND_PARTY_WALLET_NUMBER = WG2.WALLET_NUMBER
    WHERE MTI.TRANSACTION_TYPE = 'MP'
),
MTI_MR AS (
    SELECT
        MTI.TRANSFER_ID, MTI.WALLET_NUMBER, MTI.ISSUER_ID,
        CASE
            WHEN MC.CATEGORY_NAME = 'Final User' THEN 'USUARIO FINAL'
            WHEN MC.CATEGORY_NAME = 'BIMER User' THEN 'BIMER'
        END AS PAYER_CATEGORY_CODE
    FROM TRX_ITEMS MTI
    INNER JOIN read_parquet('{marketing_profile_path}', union_by_name=true) MP
        ON MTI.SECOND_PARTY_MARKETING_PROFILE_CODE = MP.MARKETING_PROFILE_CODE
    INNER JOIN read_parquet('{mtx_categories_path}', union_by_name=true) MC
        ON MP.CATEGORY_CODE = MC.CATEGORY_CODE
    WHERE MTI.TRANSACTION_TYPE = 'MR'
        AND MTI.SERVICE_TYPE IN ('ISSUERMOV','CHANGECAT')
),
REVERSAL AS (
    SELECT MTH.TRANSFER_ID, MTH.FIELD7
    FROM TRX_HEADER MTH
),
TRX_DATA_DAY AS (
    SELECT
        MTH.FIELD7,
        MTH.TRANSFER_ID,
        MTH.FTXN_ID,
        MTH.SOURCE,
        CASE WHEN CAST(MTH.PAYER_USER_ID AS VARCHAR) = 'IND012' THEN CAST(MTH.PAYER_IDENTIFIER_VALUE AS VARCHAR)
             ELSE CAST(MTH.PAYER_USER_ID AS VARCHAR) END AS PAYER_USER_ID,
        CAST(MTH.PAYEE_USER_ID AS VARCHAR) AS PAYEE_USER_ID,
        MTH.NEW_PAYEE_IDENTIFIER_VALUE AS PAYEE_IDENTIFIER_VALUE,
        MTH.NEW_PAYER_IDENTIFIER_VALUE AS PAYER_IDENTIFIER_VALUE,
        CAST(MTH.CREATED_BY AS VARCHAR) AS CREATED_BY,
        CAST(MTH.MODIFIED_BY AS VARCHAR) AS MODIFIED_BY,
        MTH.TRANSFER_DATE,
        MTH.TRANSFER_STATUS,
        MTH.TRANSFER_VALUE,
        MSC.TRANSFER_VALUE AS FEE,
        MR.PAYER_CATEGORY_CODE,
        MTH.REQUEST_GATEWAY_TYPE AS CANAL,
        -- Lógica de REMARKS exacta como Oracle líneas 115-120
        CASE
            WHEN MTH.NEW_PAYEE_IDENTIFIER_VALUE IN ('claro','sentinel')
                THEN json_extract_string(CAST(MTH.PARTNER_DATA AS VARCHAR), '$.codigoPago')
            WHEN MTH.NEW_PAYEE_IDENTIFIER_VALUE = 'bitel'
                THEN SUBSTR(CAST(MTH.FIELD8 AS VARCHAR), 1, STRPOS(CAST(MTH.FIELD8 AS VARCHAR), '@') - 1)
            WHEN MTH.NEW_PAYEE_IDENTIFIER_VALUE = 'unique'
                THEN SUBSTR(CAST(MTH.REMARKS AS VARCHAR), 1, STRPOS(CAST(MTH.REMARKS AS VARCHAR), '_') - 1)
            ELSE CAST(MTH.REMARKS AS VARCHAR)
        END AS REMARKS,
        MTH.REQUEST_GATEWAY_TYPE,
        MP.WALLET_NUMBER AS PAYER_WALLET_NUMBER,
        CASE
            WHEN MTH.SERVICE_TYPE IN ('ISSUERMOV','CHANGECAT') THEN MR.WALLET_NUMBER
            ELSE MP.SECOND_PARTY_WALLET_NUMBER
        END AS PAYEE_WALLET_NUMBER,
        -- Aplicar lógica Oracle exacta para GRADE_NAME
        UPPER(MP.GRADE) AS PAYER_GRADE,
        UPPER(MP.SECOND_GRADE) AS PAYEE_GRADE,
        ID1.ISSUER_CODE AS PAYER_ISSUER_CODE,
        ID2.ISSUER_CODE AS PAYEE_ISSUER_CODE,
        MTH.PAYER_PROVIDER_ID,
        MTH.RECONCILIATION_BY,
        MTH.FIELD2,
        -- Lógica de TRX_SERVICE exacta como Oracle líneas 134-155
        CASE
            WHEN MTH.SERVICE_TYPE='CASHIN' AND MTH.MODIFIED_BY <> 'IND012' THEN 'CASH_IN'
            WHEN MTH.SERVICE_TYPE='CASHOUT' THEN 'CASH_OUT'
            WHEN MTH.SERVICE_TYPE='P2P' THEN 'TRANSFER'
            WHEN (MTH.SERVICE_TYPE='BILLPAY' AND (MTH.PAYEE_IDENTIFIER_VALUE = 'crandes' OR
                (MTH.PAYER_USER_ID IN {special_users_tuple} OR
                 MTH.PAYEE_USER_ID IN {special_users_tuple}))) THEN 'PAYMENT'
            WHEN (MTH.SERVICE_TYPE='BILLPAY' AND (MTH.PAYEE_IDENTIFIER_VALUE <> 'crandes' OR
                (MTH.PAYER_USER_ID NOT IN {special_users_tuple} AND
                 MTH.PAYEE_USER_ID NOT IN {special_users_tuple}))) THEN 'EXTERNAL_PAYMENT'
            WHEN MTH.SERVICE_TYPE='OFFUSOUT' THEN 'PAYMENT'
            WHEN MTH.SERVICE_TYPE IN ('STOCKCRT','STOCKINT','STOCK') THEN 'DEPOSIT'
            WHEN MTH.SERVICE_TYPE IN ('C2C','INVC2C') THEN 'FLOAT_TRANSFER'
            WHEN MTH.SERVICE_TYPE IN ('MULTIDRCR2') OR
                (MTH.SERVICE_TYPE = 'CASHIN' AND MTH.MODIFIED_BY = 'IND012') THEN 'BATCH_TRANSFER'
            WHEN MTH.SERVICE_TYPE IN ('OPTW') THEN 'TRANSFER_TO_ANY_BANK_ACCOUNT'
            WHEN MTH.SERVICE_TYPE IN ('STOCKTFR') THEN 'CUSTODY_ACCOUNTS_TRANSFER'
            WHEN MTH.SERVICE_TYPE IN ('ATMCASHOUT') THEN 'CASH_OUT_ATM'
            WHEN MTH.SERVICE_TYPE IN ('ISSUERMOV','CHANGECAT') THEN 'ADJUSTMENT'
            WHEN MTH.SERVICE_TYPE IN ('TXNCORRECT') AND MTH.ATTR_3_NAME = 'BULK_PAYMENT_BATCHID'
                THEN 'REVERSAL_BATCH_TRANSFER'
            WHEN MTH.SERVICE_TYPE IN ('TXNCORRECT') AND
                (MTH.ATTR_3_VALUE IN ('CASHOUT','CASHIN','ATMCASHOUT') OR MTH.ATTR_3_VALUE IS NULL)
                THEN 'REVERSAL'
            WHEN MTH.SERVICE_TYPE IN ('TXNCORRECT') AND MTH.ATTR_3_VALUE='BILLPAY' THEN
                CASE
                    WHEN (MTH.PAYEE_IDENTIFIER_VALUE = 'crandes' OR
                        (MTH.PAYER_USER_ID IN {special_users_tuple} OR
                         MTH.PAYEE_USER_ID IN {special_users_tuple})) THEN 'TRANSFER'
                    ELSE 'REFUND'
                END
            ELSE MTH.SERVICE_TYPE
        END AS TRX_SERVICE
    FROM TRX_HEADER MTH
    LEFT JOIN MTI_MP MP ON MTH.TRANSFER_ID = MP.TRANSFER_ID
    LEFT JOIN MTI_MR MR ON MTH.TRANSFER_ID = MR.TRANSFER_ID
    LEFT JOIN MTI_SCP MSC ON MTH.TRANSFER_ID = MSC.TRANSFER_ID
    LEFT JOIN read_parquet('{issuer_details_path}', union_by_name=true) ID1
        ON MP.ISSUER_ID = ID1.ISSUER_ID
    LEFT JOIN read_parquet('{issuer_details_path}', union_by_name=true) ID2
        ON MP.SECOND_PARTY_ISSUER_ID = ID2.ISSUER_ID
)

-- ========================================================================
-- DEDUPLICATED_DATA - COPIA EXACTA del pipeline original líneas 681-847
-- ========================================================================
, DEDUPLICATED_DATA AS (
    SELECT
        MTH.FIELD7 AS "TransferID",                    -- 1
        MTH.TRANSFER_ID AS "TransferID_Mob",           -- 2
        MTH.FTXN_ID AS "ExternalTransactionID",       -- 3
        MTH.SOURCE AS "Source",                       -- 4
        UPAYER.M_USER_ID AS "FromID_Mobiquity",       -- 5
        UPAYEE.M_USER_ID AS "ToID_Mobiquity",         -- 6
        MTH.PAYER_IDENTIFIER_VALUE AS "From_Identifier", -- 7
        MTH.PAYEE_IDENTIFIER_VALUE AS "To_Identifier",   -- 8
        MTH.CREATED_BY AS "CreatedBy",                -- 9
        MTH.MODIFIED_BY AS "ModifiedBy",              -- 10
        CAST(MTH.TRANSFER_DATE AS TIMESTAMP) AS "TransferDate", -- 11
        MTH.TRANSFER_STATUS AS "TransferStatus",      -- 12
        MTH.TRANSFER_VALUE AS "Amount",               -- 13
        MTH.FEE AS "Fee",                            -- 14
        MTH.CANAL AS "Gateway_Code",                 -- 15
        MTH.REMARKS AS "Remarks",                    -- 16
        UPAYER.ACCOUNT_ID AS "From_AccountID",       -- 17
        UPAYEE.ACCOUNT_ID AS "To_AccountID",         -- 18
        -- ORDEN ORACLE: Posiciones 19-31 (From_AccountID_Mobiquity a To_LoginID)
        -- From_AccountID_Mobiquity: LÓGICA EXACTA ORACLE (línea 182 SP_PRE_LOG_TRX)
        -- Oracle usa directamente MTH.PAYER_WALLET_NUMBER de MTX_TRANSACTION_ITEMS
        MTH.PAYER_WALLET_NUMBER AS "From_AccountID_Mobiquity",              -- 19
        -- To_AccountID_Mobiquity: LÓGICA EXACTA ORACLE (línea 183 SP_PRE_LOG_TRX)
        -- Oracle usa directamente MTH.PAYEE_WALLET_NUMBER de MTX_TRANSACTION_ITEMS
        MTH.PAYEE_WALLET_NUMBER AS "To_AccountID_Mobiquity",                -- 20
        MTH.PAYER_GRADE AS "From_Grade",                -- 21
        MTH.PAYEE_GRADE AS "To_Grade",                  -- 22
        MTH.PAYER_ISSUER_CODE AS "From_BankDomain",     -- 23
        MTH.PAYEE_ISSUER_CODE AS "To_BankDomain",       -- 24
        101 AS "CurrencyCode",                          -- 25
        MTH.RECONCILIATION_BY AS "ReversalID",          -- 26
        -- Mapear TRX_SERVICE a TransactionType
        CASE
            WHEN MTH.TRX_SERVICE IN ('REVERSAL_BATCH_TRANSFER') AND
                (UPAYER.LOGIN_ID = 'VIRTUALINTEROPCFBIM' OR UPAYEE.LOGIN_ID = 'VIRTUALINTEROPCFBIM')
                THEN 'REVERSAL'
            ELSE MTH.TRX_SERVICE
        END AS "TransactionType",                       -- 27
        UPAYER.MSISDN AS "From_Msisdn",                 -- 28
        UPAYEE.MSISDN AS "To_Msisdn",                   -- 29
        -- From_LoginID: NULL para la mayoría de usuarios (como Oracle USER_DATA_TRX)
        -- Solo mantener LOGIN_ID para usuarios específicos (agentes, billers, etc.)
        CASE
            WHEN UPAYER.PROFILE IN ('AGENTE VIRTUAL', 'Biller') OR
                 UPAYER.LOGIN_ID IN ('VIRTUALINTEROPCFBIM', 'COMPWKASNET', 'COMPWVIRTUAL163', 'COMPWFULLCARGA', 'COMPW0165', 'COMPW0086') OR
                 (UPAYER.LOGIN_ID LIKE '51%' AND LENGTH(UPAYER.LOGIN_ID) = 11) OR
                 UPAYER.LOGIN_ID IN ('backus', 'lindley')
            THEN UPAYER.LOGIN_ID
            ELSE NULL
        END AS "From_LoginID",                          -- 30
        -- To_LoginID: Misma lógica que From_LoginID
        CASE
            WHEN UPAYEE.PROFILE IN ('AGENTE VIRTUAL', 'Biller') OR
                 UPAYEE.LOGIN_ID IN ('VIRTUALINTEROPCFBIM', 'COMPWKASNET', 'COMPWVIRTUAL163', 'COMPWFULLCARGA', 'COMPW0165', 'COMPW0086') OR
                 (UPAYEE.LOGIN_ID LIKE '51%' AND LENGTH(UPAYEE.LOGIN_ID) = 11) OR
                 UPAYEE.LOGIN_ID IN ('backus', 'lindley')
            THEN UPAYEE.LOGIN_ID
            ELSE NULL
        END AS "To_LoginID",                            -- 31
        UPAYER.WORKSPACE_ID AS "From_Workspace",           -- 32
        UPAYEE.WORKSPACE_ID AS "To_Workspace",           -- 33
        UPAYER.USER_ID AS "FromID",                      -- 34
        UPAYEE.USER_ID AS "ToID",                        -- 35
        'Soles' AS "Currency",                           -- 36
        -- Mapear perfiles a nombres Oracle
        CASE
            WHEN UPAYER.PROFILE LIKE '%PROVEEDOR%' THEN UPAYER.PROFILE
            WHEN MTH.TRX_SERVICE IN ('REFUND') THEN
                CASE
                    WHEN UPAYER.PROFILE <> 'COMERCIO' AND MTH.PAYER_IDENTIFIER_VALUE IN ('backus', 'lindley', 'unique')
                        -- Para REFUND con proveedores específicos: usar PROVEEDOR DE SERVICIOS
                        THEN UPPER(MTH.PAYER_IDENTIFIER_VALUE) || ' PROVEEDOR DE SERVICIOS'
                    WHEN UPAYER.PROFILE <> 'COMERCIO'
                        THEN UPPER(MTH.PAYER_IDENTIFIER_VALUE) || ' ' || UPAYER.PROFILE
                    ELSE 'FCOMPARTAMOS COMERCIO'
                END
            WHEN MTH.TRX_SERVICE = 'ADJUSTMENT' THEN
                MTH.PAYER_ISSUER_CODE || ' ' || MTH.PAYER_CATEGORY_CODE
            WHEN UPAYER.PROFILE = 'Biller' THEN
                -- Para Biller: usar COMERCIO (igual que Oracle)
                MTH.PAYER_ISSUER_CODE || ' COMERCIO'
            ELSE MTH.PAYER_ISSUER_CODE || ' ' || UPAYER.PROFILE
        END AS "From_Profile",                          -- 37
        CASE
            WHEN UPAYEE.PROFILE LIKE '%PROVEEDOR%' THEN UPAYEE.PROFILE
            WHEN MTH.TRX_SERVICE = 'EXTERNAL_PAYMENT' THEN
                -- Para EXTERNAL_PAYMENT: construir como Oracle con mapeo especial
                CASE
                    WHEN UPAYEE.LOGIN_ID = 'airtimeclaro' THEN 'CLARO PROVEEDOR DE SERVICIOS'
                    ELSE UPPER(UPAYEE.LOGIN_ID) || ' PROVEEDOR DE SERVICIOS'
                END
            WHEN MTH.TRX_SERVICE = 'PAYMENT' AND UPAYEE.PROFILE = 'Biller' THEN
                -- Para PAYMENT: usar COMERCIO en lugar de Biller
                MTH.PAYEE_ISSUER_CODE || ' COMERCIO'
            ELSE MTH.PAYEE_ISSUER_CODE || ' ' || UPAYEE.PROFILE
        END AS "To_Profile",                            -- 38
        CASE
            WHEN MTH.TRX_SERVICE IN ('REVERSAL','REFUND') THEN TP.FIELD7
            WHEN MTH.TRX_SERVICE IN ('REVERSAL_BATCH_TRANSFER') AND
                (UPAYER.LOGIN_ID = 'VIRTUALINTEROPCFBIM' OR UPAYEE.LOGIN_ID = 'VIRTUALINTEROPCFBIM')
                THEN TP.FIELD7
            ELSE NULL  -- ⭐ CORRECCIÓN: NULL en lugar de string vacío
        END AS "Comment",                               -- 39
        CASE
            WHEN MTH.TRX_SERVICE IN ('BATCH_TRANSFER','REVERSAL_BATCH_TRANSFER') THEN 'internal'
            WHEN MTH.TRX_SERVICE = 'TRANSFER_TO_ANY_BANK_ACCOUNT' THEN 'http-pdp'
            WHEN MTH.CANAL = 'WEB' AND MTH.TRX_SERVICE = 'CUSTODY_ACCOUNTS_TRANSFER' THEN 'http-adm'
            WHEN MTH.TRX_SERVICE = 'DEPOSIT' THEN 'http-adm'
            WHEN MTH.TRX_SERVICE = 'REFUND' THEN 'http-awspdp'
            WHEN MTH.TRX_SERVICE = 'FLOAT_TRANSFER' THEN 'http-partner'
            WHEN MTH.SOURCE IS NULL AND MTH.TRX_SERVICE = 'PAYMENT' THEN 'http-xml_awspdp'
            WHEN MTH.CANAL = 'WEB' AND MTH.TRX_SERVICE = 'TRANSFER' AND UPAYER.PROFILE = 'COMERCIO'
                THEN 'http-awspdp'
            WHEN MTH.CANAL = 'WEB' AND MTH.SOURCE IS NULL AND MTH.TRX_SERVICE = 'CASH_IN' AND
                UPAYER.PROFILE = 'SUPER AGENTE' AND UPAYEE.PROFILE IN ('USUARIO FINAL','BIMER')
                THEN 'http-fcompartamos_ofi'
            WHEN MTH.CANAL = 'WEB' AND MTH.TRX_SERVICE = 'CASH_IN' AND
                MTH.PAYER_ISSUER_CODE = 'CRANDES' AND UPAYER.PROFILE = 'AGENCIA' THEN 'http-partner'
            WHEN MTH.CANAL = 'WEB' AND MTH.TRX_SERVICE = 'REVERSAL' AND
                UPAYER.PROFILE IN ('USUARIO FINAL','BIMER') AND MTH.PAYEE_ISSUER_CODE = 'CRANDES' AND
                UPAYEE.PROFILE = 'AGENCIA' THEN 'http-partner'
            WHEN MTH.CANAL = 'WEB' AND MTH.TRX_SERVICE = 'CASH_IN' AND
                UPAYER.LOGIN_ID = 'COMPWVIRTUAL163' THEN 'http-fcompartamos_ofi'
            WHEN MTH.CANAL = 'WEB' AND MTH.TRX_SERVICE = 'CASH_OUT' AND
                MTH.PAYEE_ISSUER_CODE = 'CRANDES' AND UPAYEE.PROFILE = 'AGENCIA' THEN 'http-xml_ms'
            WHEN MTH.CANAL = 'WEB' AND MTH.TRX_SERVICE = 'REVERSAL' AND
                (UPAYEE.LOGIN_ID = 'VIRTUALINTEROPCFBIM' OR UPAYER.LOGIN_ID = 'VIRTUALINTEROPCFBIM')
                THEN 'http-fcompartamos_niubiz_interope'
            WHEN MTH.CANAL = 'WEB' AND MTH.TRX_SERVICE = 'REVERSAL' AND
                UPAYEE.LOGIN_ID = 'COMPWKASNET' THEN 'http-ci_kasnet_partner'
            WHEN MTH.CANAL = 'WEB' AND MTH.TRX_SERVICE = 'REVERSAL' AND
                UPAYER.LOGIN_ID = 'COMPWVIRTUAL163' THEN 'http-fcompartamos_simp'
            WHEN MTH.CANAL = 'WEB' AND MTH.TRX_SERVICE = 'REVERSAL' AND
                UPAYEE.LOGIN_ID = 'COMPWVIRTUAL163' THEN 'http-fcompartamos_app'
            WHEN MTH.CANAL = 'WEB' AND MTH.TRX_SERVICE = 'REVERSAL' AND
                UPAYER.LOGIN_ID = 'COMPWFULLCARGA' THEN 'http-fcompartamos_fullcarga'
            WHEN MTH.CANAL = 'WEB' AND MTH.TRX_SERVICE = 'REVERSAL' AND
                (UPAYEE.LOGIN_ID = 'VIRTUALINTEROPCFCCE' OR UPAYER.LOGIN_ID = 'VIRTUALINTEROPCFCCE')
                THEN 'http-fcompartamos_cce_interope'
            WHEN MTH.TRX_SERVICE = 'ADJUSTMENT' THEN 'http-awspdp'
            ELSE CASE
                WHEN MTH.SOURCE LIKE 'http-%' THEN MTH.SOURCE
                WHEN MTH.SOURCE IS NOT NULL THEN 'http-' || MTH.SOURCE
                ELSE MTH.SOURCE
            END
        END AS "Context",                               -- 40
        UPAYER.ID_TYPE AS "From_DocumentType",          -- 41
        UPAYEE.ID_TYPE AS "To_DocumentType",            -- 42
        ROW_NUMBER() OVER (PARTITION BY MTH.FIELD7 ORDER BY MTH.TRANSFER_DATE) as rn
FROM TRX_DATA_DAY MTH
LEFT JOIN USER_DATA UPAYER ON MTH.PAYER_USER_ID = UPAYER.M_USER_ID AND UPAYER.M_USER_ID IS NOT NULL
LEFT JOIN USER_DATA UPAYEE ON MTH.PAYEE_USER_ID = UPAYEE.M_USER_ID AND UPAYEE.M_USER_ID IS NOT NULL
LEFT JOIN read_parquet('{sys_service_provider_path}', union_by_name=true) SSP
    ON MTH.PAYER_PROVIDER_ID = SSP.PROVIDER_ID
LEFT JOIN REVERSAL TP ON MTH.RECONCILIATION_BY = TP.TRANSFER_ID AND
    MTH.TRX_SERVICE IN ('REVERSAL','REFUND','REVERSAL_BATCH_TRANSFER')
WHERE 1=1
    -- CODE NINJA MASTER: FILTRO ORACLE EXACTO - LÍNEA 253 SP_PRE_LOG_TRX.sql
    AND MTH.TRX_SERVICE NOT IN ('MULTIDRCR','FTBOA','MERCHPAY','TXNCORRECT','MULTIDRCR3')
)
SELECT * EXCLUDE (rn)
FROM DEDUPLICATED_DATA
WHERE rn = 1

FROM TRX_DATA_DAY TDD
-- JOINs para obtener datos de USER_DATA
LEFT JOIN USER_DATA UD_PAYER ON TDD.PAYER_USER_ID = UD_PAYER.USER_ID
LEFT JOIN USER_DATA UD_PAYEE ON TDD.PAYEE_USER_ID = UD_PAYEE.USER_ID
-- JOINs para obtener WALLET_NUMBER más reciente (US.xxxxx)
LEFT JOIN MTX_WALLET_LATEST MW_PAYER ON TDD.PAYER_USER_ID = MW_PAYER.USER_ID AND MW_PAYER.RN = 1
LEFT JOIN MTX_WALLET_LATEST MW_PAYEE ON TDD.PAYEE_USER_ID = MW_PAYEE.USER_ID AND MW_PAYEE.RN = 1
-- JOINs especiales para caso 945661
LEFT JOIN MTX_WALLET_945661 MW945_DESC ON TDD.PAYER_USER_ID = MW945_DESC.USER_ID AND MW945_DESC.RN_DESC = 1
LEFT JOIN MTX_WALLET_945661 MW945_ASC ON TDD.PAYER_USER_ID = MW945_ASC.USER_ID AND MW945_ASC.RN_ASC = 1

-- CODE NINJA MASTER: FILTRO ORACLE EXACTO - LÍNEA 842 pipeline original
-- Excluir TRX_SERVICE específicos que no deben aparecer en el reporte final
-- El filtro se aplica al TRX_SERVICE calculado, no al SERVICE_TYPE original
WHERE TDD.TRX_SERVICE NOT IN ('MULTIDRCR','FTBOA','MERCHPAY','TXNCORRECT','MULTIDRCR3')

ORDER BY TDD.TRANSFER_DATE, TDD.TRANSFER_ID
