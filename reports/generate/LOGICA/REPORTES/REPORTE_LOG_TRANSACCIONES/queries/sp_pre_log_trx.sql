-- ========================================================================
-- SP_PRE_LOG_TRX - Migrado del pipeline_log_transacciones_duckdb.py
-- Procesa transacciones desde MTX_TRANSACTION_HEADER/ITEMS
-- Aplica lógica de negocio compleja (TRX_SERVICE, CONTEXT, etc.)
-- Genera: PRE_LOG_TRX.parquet
-- ========================================================================
-- CODE NINJA MASTER: FILTROS ORACLE EXACTOS PARA HOMOLOGACIÓN 100%
-- Query SQL EXACTA como SP_PRE_LOG_TRX líneas 27-253

WITH
TRX_HEADER AS (
    SELECT
        MTH.*,
        REPLACE(CAST(MTH.PAYEE_IDENTIFIER_VALUE AS VARCHAR),'1','') AS NEW_PAYEE_IDENTIFIER_VALUE,
        REPLACE(CAST(MTH.PAYER_IDENTIFIER_VALUE AS VARCHAR),'1','') AS NEW_PAYER_IDENTIFIER_VALUE
    FROM read_parquet('{mtx_transaction_header_path}', union_by_name=true) MTH
    LEFT JOIN read_parquet('{sys_service_types_path}', union_by_name=true) SST
        ON MTH.SERVICE_TYPE = SST.SERVICE_TYPE
    -- CODE NINJA: Ya no necesario filtrar por fecha - solo leemos archivos del día específico
    -- WHERE CAST(MTH.TRANSFER_DATE AS TIMESTAMP) >= CAST('{fecha_inicio}' AS TIMESTAMP)
    --     AND CAST(MTH.TRANSFER_DATE AS TIMESTAMP) < CAST('{fecha_inicio}' AS TIMESTAMP) + INTERVAL '1 day'
    WHERE 1=1
        AND MTH.TRANSFER_STATUS IN ('TA','TS')
        AND MTH.TRANSFER_VALUE <> 0
        AND SST.IS_FINANCIAL = 'Y'
        -- CODE NINJA MASTER: FILTRO EXPLÍCITO DE FECHA PARA RESPETAR ARGUMENTO
        -- Garantiza que solo se procesen registros del día especificado
        AND CAST(MTH.TRANSFER_DATE AS DATE) = CAST('{fecha_inicio}' AS DATE)
),
TRX_ITEMS AS (
    SELECT
        MTI.TRANSFER_ID, MTI.TRANSFER_VALUE, MTI.WALLET_NUMBER,
        MTI.SECOND_PARTY_WALLET_NUMBER, MTI.SERVICE_TYPE,
        MTI.ISSUER_ID, MTI.SECOND_PARTY_ISSUER_ID,
        MTI.SECOND_PARTY_MARKETING_PROFILE_CODE, MTI.TRANSACTION_TYPE
    FROM read_parquet('{mtx_transaction_items_path}', union_by_name=true) MTI
    INNER JOIN TRX_HEADER MTH ON MTI.TRANSFER_ID = MTH.TRANSFER_ID
),
MTX_WALLET_LATEST AS (
    SELECT
        USER_ID,
        WALLET_NUMBER,
        ROW_NUMBER() OVER (PARTITION BY USER_ID ORDER BY MODIFIED_ON DESC) as RN
    FROM read_parquet('{mtx_wallet_path}', union_by_name=true)
    WHERE USER_ID LIKE 'US.%' OR USER_ID = '945661'  -- US.xxxxx + caso especial 945661
),
MTX_WALLET_945661 AS (
    SELECT
        USER_ID,
        WALLET_NUMBER,
        STATUS,
        ROW_NUMBER() OVER (PARTITION BY USER_ID ORDER BY MODIFIED_ON DESC) as RN_DESC,
        ROW_NUMBER() OVER (PARTITION BY USER_ID ORDER BY MODIFIED_ON ASC) as RN_ASC
    FROM read_parquet('{mtx_wallet_path}', union_by_name=true)
    WHERE USER_ID = '945661'
),
-- CTE WALLETS: Replica EXACTAMENTE Oracle SP_PRE_LOG_USR líneas 12-20
WALLETS AS (
    SELECT
        MW.USER_ID,
        MW.WALLET_NUMBER,
        MW.ISSUER_ID,
        MW.USER_GRADE,
        -- LÓGICA EXACTA DE ORACLE: Solo MODIFIED_ON DESC, SIN filtro STATUS
        ROW_NUMBER() OVER (PARTITION BY MW.USER_ID ORDER BY MW.MODIFIED_ON DESC) AS ORDEN
    FROM read_parquet('{mtx_wallet_path}', union_by_name=true) MW
),
USER_DATA AS (
    SELECT DISTINCT
        UD.USER_ID,
        UD.O_USER_ID AS M_USER_ID,
        UD.PROFILE_TRX AS PROFILE,
        UD.WALLET_NUMBER AS ACCOUNT_ID,
        -- Aplicar lógica EXACTA de Oracle SP_PRE_LOG_USR líneas 33-37
        CASE
            WHEN UD.O_USER_ID LIKE 'US.%' THEN
                COALESCE(MW.WALLET_NUMBER, UD.WALLET_NUMBER)
            WHEN UD.O_USER_ID = '945661' THEN
                -- Caso especial: usar WALLET_NUMBER original para cálculos base
                UD.WALLET_NUMBER
            ELSE
                -- LÓGICA EXACTA ORACLE: ATTR8 o WALLET_NUMBER procesado
                CASE
                    WHEN UP.ATTR8 IS NOT NULL THEN UP.ATTR8
                    WHEN LENGTH(REPLACE(MW.WALLET_NUMBER,'UA.','')) > 15 THEN
                        SUBSTR(REPLACE(MW.WALLET_NUMBER,'UA.',''), -15)
                    ELSE REPLACE(MW.WALLET_NUMBER,'UA.','')
                END
        END AS WALLET_NUMBER,
        UD.MSISDN,
        UD.USER_CODE,
        UD.LOGIN_ID,
        UD.WORKSPACE_ID,
        UD.ISSUER_CODE,
        UD.ID_TYPE
    FROM read_parquet('{user_data_trx_path}', union_by_name=true) UD
    -- JOIN EXACTO de Oracle SP_PRE_LOG_USR línea 113: ORDEN=1 (más reciente)
    LEFT JOIN WALLETS MW ON UD.O_USER_ID = MW.USER_ID AND MW.ORDEN = 1
    -- JOIN con USER_PROFILE para obtener ATTR8 (lógica Oracle líneas 33-37)
    LEFT JOIN read_parquet('{user_profile_path}', union_by_name=true) UP
        ON UD.O_USER_ID = UP.USER_ID
),
MTI_SCP AS (
    SELECT MTI.TRANSFER_ID, MTI.TRANSFER_VALUE
    FROM TRX_ITEMS MTI
    WHERE MTI.TRANSACTION_TYPE = 'SCP'
),
WALLETS_GRADE AS (
    SELECT MW.ISSUER_ID, MW.WALLET_NUMBER, CG.GRADE_NAME
    FROM read_parquet('{mtx_wallet_path}', union_by_name=true) MW
    INNER JOIN read_parquet('{channel_grades_path}', union_by_name=true) CG
        ON MW.USER_GRADE = CG.GRADE_CODE
),
MTI_MP AS (
    SELECT
        MTI.TRANSFER_ID, MTI.WALLET_NUMBER, MTI.SECOND_PARTY_WALLET_NUMBER,
        MTI.ISSUER_ID, MTI.SECOND_PARTY_ISSUER_ID,
        WG.GRADE_NAME AS GRADE, WG2.GRADE_NAME AS SECOND_GRADE
    FROM TRX_ITEMS MTI
    LEFT JOIN WALLETS_GRADE WG ON MTI.WALLET_NUMBER = WG.WALLET_NUMBER
    LEFT JOIN WALLETS_GRADE WG2 ON MTI.SECOND_PARTY_WALLET_NUMBER = WG2.WALLET_NUMBER
    WHERE MTI.TRANSACTION_TYPE = 'MP'
),
MTI_MR AS (
    SELECT
        MTI.TRANSFER_ID, MTI.WALLET_NUMBER, MTI.ISSUER_ID,
        CASE
            WHEN MC.CATEGORY_NAME = 'Final User' THEN 'USUARIO FINAL'
            WHEN MC.CATEGORY_NAME = 'BIMER User' THEN 'BIMER'
        END AS PAYER_CATEGORY_CODE
    FROM TRX_ITEMS MTI
    INNER JOIN read_parquet('{marketing_profile_path}', union_by_name=true) MP
        ON MTI.SECOND_PARTY_MARKETING_PROFILE_CODE = MP.MARKETING_PROFILE_CODE
    INNER JOIN read_parquet('{mtx_categories_path}', union_by_name=true) MC
        ON MP.CATEGORY_CODE = MC.CATEGORY_CODE
    WHERE MTI.TRANSACTION_TYPE = 'MR'
        AND MTI.SERVICE_TYPE IN ('ISSUERMOV','CHANGECAT')
),
REVERSAL AS (
    SELECT MTH.TRANSFER_ID, MTH.FIELD7
    FROM TRX_HEADER MTH
),
TRX_DATA_DAY AS (
    SELECT
        MTH.FIELD7,
        MTH.TRANSFER_ID,
        MTH.FTXN_ID,
        MTH.SOURCE,
        CASE WHEN CAST(MTH.PAYER_USER_ID AS VARCHAR) = 'IND012' THEN CAST(MTH.PAYER_IDENTIFIER_VALUE AS VARCHAR)
             ELSE CAST(MTH.PAYER_USER_ID AS VARCHAR) END AS PAYER_USER_ID,
        CAST(MTH.PAYEE_USER_ID AS VARCHAR) AS PAYEE_USER_ID,
        MTH.NEW_PAYEE_IDENTIFIER_VALUE AS PAYEE_IDENTIFIER_VALUE,
        MTH.NEW_PAYER_IDENTIFIER_VALUE AS PAYER_IDENTIFIER_VALUE,
        CAST(MTH.CREATED_BY AS VARCHAR) AS CREATED_BY,
        CAST(MTH.MODIFIED_BY AS VARCHAR) AS MODIFIED_BY,
        MTH.TRANSFER_DATE,
        MTH.TRANSFER_STATUS,
        MTH.TRANSFER_VALUE,
        MSC.TRANSFER_VALUE AS FEE,
        MR.PAYER_CATEGORY_CODE,
        MTH.REQUEST_GATEWAY_TYPE AS CANAL,
        -- Lógica de REMARKS exacta como Oracle líneas 115-120
        CASE
            WHEN MTH.NEW_PAYEE_IDENTIFIER_VALUE IN ('claro','sentinel')
                THEN json_extract_string(CAST(MTH.PARTNER_DATA AS VARCHAR), '$.codigoPago')
            WHEN MTH.NEW_PAYEE_IDENTIFIER_VALUE = 'bitel'
                THEN SUBSTR(CAST(MTH.FIELD8 AS VARCHAR), 1, STRPOS(CAST(MTH.FIELD8 AS VARCHAR), '@') - 1)
            WHEN MTH.NEW_PAYEE_IDENTIFIER_VALUE = 'unique'
                THEN SUBSTR(CAST(MTH.REMARKS AS VARCHAR), 1, STRPOS(CAST(MTH.REMARKS AS VARCHAR), '_') - 1)
            ELSE CAST(MTH.REMARKS AS VARCHAR)
        END AS REMARKS,
        MTH.REQUEST_GATEWAY_TYPE,
        MP.WALLET_NUMBER AS PAYER_WALLET_NUMBER,
        CASE
            WHEN MTH.SERVICE_TYPE IN ('ISSUERMOV','CHANGECAT') THEN MR.WALLET_NUMBER
            ELSE MP.SECOND_PARTY_WALLET_NUMBER
        END AS PAYEE_WALLET_NUMBER,
        -- Aplicar lógica Oracle exacta para GRADE_NAME
        UPPER(MP.GRADE) AS PAYER_GRADE,
        UPPER(MP.SECOND_GRADE) AS PAYEE_GRADE,
        ID1.ISSUER_CODE AS PAYER_ISSUER_CODE,
        ID2.ISSUER_CODE AS PAYEE_ISSUER_CODE,
        MTH.PAYER_PROVIDER_ID,
        MTH.RECONCILIATION_BY,
        MTH.FIELD2,
        -- Lógica de TRX_SERVICE exacta como Oracle líneas 134-155
        CASE
            WHEN MTH.SERVICE_TYPE='CASHIN' AND MTH.MODIFIED_BY <> 'IND012' THEN 'CASH_IN'
            WHEN MTH.SERVICE_TYPE='CASHOUT' THEN 'CASH_OUT'
            WHEN MTH.SERVICE_TYPE='P2P' THEN 'TRANSFER'
            WHEN (MTH.SERVICE_TYPE='BILLPAY' AND (MTH.PAYEE_IDENTIFIER_VALUE = 'crandes' OR
                (MTH.PAYER_USER_ID IN {special_users_tuple} OR
                 MTH.PAYEE_USER_ID IN {special_users_tuple}))) THEN 'PAYMENT'
            WHEN (MTH.SERVICE_TYPE='BILLPAY' AND (MTH.PAYEE_IDENTIFIER_VALUE <> 'crandes' OR
                (MTH.PAYER_USER_ID NOT IN {special_users_tuple} AND
                 MTH.PAYEE_USER_ID NOT IN {special_users_tuple}))) THEN 'EXTERNAL_PAYMENT'
            WHEN MTH.SERVICE_TYPE='OFFUSOUT' THEN 'PAYMENT'
            WHEN MTH.SERVICE_TYPE IN ('STOCKCRT','STOCKINT','STOCK') THEN 'DEPOSIT'
            WHEN MTH.SERVICE_TYPE IN ('C2C','INVC2C') THEN 'FLOAT_TRANSFER'
            WHEN MTH.SERVICE_TYPE IN ('MULTIDRCR2') OR
                (MTH.SERVICE_TYPE = 'CASHIN' AND MTH.MODIFIED_BY = 'IND012') THEN 'BATCH_TRANSFER'
            WHEN MTH.SERVICE_TYPE IN ('OPTW') THEN 'TRANSFER_TO_ANY_BANK_ACCOUNT'
            WHEN MTH.SERVICE_TYPE IN ('STOCKTFR') THEN 'CUSTODY_ACCOUNTS_TRANSFER'
            WHEN MTH.SERVICE_TYPE IN ('ATMCASHOUT') THEN 'CASH_OUT_ATM'
            WHEN MTH.SERVICE_TYPE IN ('ISSUERMOV','CHANGECAT') THEN 'ADJUSTMENT'
            WHEN MTH.SERVICE_TYPE IN ('TXNCORRECT') AND MTH.ATTR_3_NAME = 'BULK_PAYMENT_BATCHID'
                THEN 'REVERSAL_BATCH_TRANSFER'
            WHEN MTH.SERVICE_TYPE IN ('TXNCORRECT') AND
                (MTH.ATTR_3_VALUE IN ('CASHOUT','CASHIN','ATMCASHOUT') OR MTH.ATTR_3_VALUE IS NULL)
                THEN 'REVERSAL'
            WHEN MTH.SERVICE_TYPE IN ('TXNCORRECT') AND MTH.ATTR_3_VALUE='BILLPAY' THEN
                CASE
                    WHEN (MTH.PAYEE_IDENTIFIER_VALUE = 'crandes' OR
                        (MTH.PAYER_USER_ID IN {special_users_tuple} OR
                         MTH.PAYEE_USER_ID IN {special_users_tuple})) THEN 'TRANSFER'
                    ELSE 'REFUND'
                END
            ELSE MTH.SERVICE_TYPE
        END AS TRX_SERVICE
    FROM TRX_HEADER MTH
    LEFT JOIN MTI_MP MP ON MTH.TRANSFER_ID = MP.TRANSFER_ID
    LEFT JOIN MTI_MR MR ON MTH.TRANSFER_ID = MR.TRANSFER_ID
    LEFT JOIN MTI_SCP MSC ON MTH.TRANSFER_ID = MSC.TRANSFER_ID
    LEFT JOIN read_parquet('{issuer_details_path}', union_by_name=true) ID1
        ON MP.ISSUER_ID = ID1.ISSUER_ID
    LEFT JOIN read_parquet('{issuer_details_path}', union_by_name=true) ID2
        ON MP.SECOND_PARTY_ISSUER_ID = ID2.ISSUER_ID
)

-- ========================================================================
-- QUERY PRINCIPAL: Genera el resultado final PRE_LOG_TRX
-- Combina todas las CTEs anteriores con lógica de negocio compleja
-- ========================================================================
SELECT
    TDD.FIELD7 AS "TransactionID",
    TDD.TRANSFER_ID AS "TransferID",
    TDD.FTXN_ID AS "FtxnID",
    TDD.SOURCE AS "Source",
    TDD.TRANSFER_DATE AS "DateTime",
    TDD.TRX_SERVICE AS "TrxService",

    -- LÓGICA FROMID_MOBIQUITY: Mapeo exacto Oracle líneas 156-170
    CASE
        WHEN TDD.PAYER_USER_ID LIKE 'US.%' THEN TDD.PAYER_USER_ID
        WHEN TDD.PAYER_USER_ID = '945661' THEN
            -- Caso especial 945661: usar lógica específica
            CASE
                WHEN MW945_DESC.USER_ID IS NOT NULL THEN MW945_DESC.USER_ID
                ELSE TDD.PAYER_USER_ID
            END
        ELSE
            COALESCE(UD_PAYER.M_USER_ID, TDD.PAYER_USER_ID)
    END AS "FromID_Mobiquity",

    -- LÓGICA TOID_MOBIQUITY: Mapeo exacto Oracle líneas 171-185
    CASE
        WHEN TDD.PAYEE_USER_ID LIKE 'US.%' THEN TDD.PAYEE_USER_ID
        WHEN TDD.PAYEE_USER_ID = '945661' THEN
            -- Caso especial 945661: usar lógica específica
            CASE
                WHEN MW945_DESC.USER_ID IS NOT NULL THEN MW945_DESC.USER_ID
                ELSE TDD.PAYEE_USER_ID
            END
        ELSE
            COALESCE(UD_PAYEE.M_USER_ID, TDD.PAYEE_USER_ID)
    END AS "ToID_Mobiquity",

    -- LÓGICA FROM_ACCOUNTID: Mapeo exacto Oracle líneas 186-200
    CASE
        WHEN TDD.PAYER_USER_ID LIKE 'US.%' THEN
            COALESCE(MW_PAYER.WALLET_NUMBER, TDD.PAYER_WALLET_NUMBER)
        WHEN TDD.PAYER_USER_ID = '945661' THEN
            -- Caso especial 945661: usar WALLET_NUMBER específico
            CASE
                WHEN MW945_ASC.WALLET_NUMBER IS NOT NULL THEN MW945_ASC.WALLET_NUMBER
                ELSE TDD.PAYER_WALLET_NUMBER
            END
        ELSE
            COALESCE(UD_PAYER.WALLET_NUMBER, TDD.PAYER_WALLET_NUMBER)
    END AS "From_AccountID",

    -- LÓGICA TO_ACCOUNTID: Mapeo exacto Oracle líneas 201-215
    CASE
        WHEN TDD.PAYEE_USER_ID LIKE 'US.%' THEN
            COALESCE(MW_PAYEE.WALLET_NUMBER, TDD.PAYEE_WALLET_NUMBER)
        WHEN TDD.PAYEE_USER_ID = '945661' THEN
            -- Caso especial 945661: usar WALLET_NUMBER específico
            CASE
                WHEN MW945_ASC.WALLET_NUMBER IS NOT NULL THEN MW945_ASC.WALLET_NUMBER
                ELSE TDD.PAYEE_WALLET_NUMBER
            END
        ELSE
            COALESCE(UD_PAYEE.WALLET_NUMBER, TDD.PAYEE_WALLET_NUMBER)
    END AS "To_AccountID",

    TDD.TRANSFER_VALUE/100 AS "Amount",
    TDD.FEE/100 AS "Fee",
    TDD.TRANSFER_STATUS AS "TransferStatus",
    TDD.PAYER_CATEGORY_CODE AS "PayerCategoryCode",
    TDD.CANAL AS "Canal",
    TDD.REMARKS AS "Remarks",
    TDD.REQUEST_GATEWAY_TYPE AS "RequestGatewayType",
    TDD.PAYER_GRADE AS "PayerGrade",
    TDD.PAYEE_GRADE AS "PayeeGrade",
    TDD.PAYER_ISSUER_CODE AS "PayerIssuerCode",
    TDD.PAYEE_ISSUER_CODE AS "PayeeIssuerCode",
    TDD.PAYER_PROVIDER_ID AS "PayerProviderID",
    TDD.RECONCILIATION_BY AS "ReconciliationBy",
    TDD.FIELD2 AS "Field2",

    -- LÓGICA TRANSACTIONTYPE: Mapeo exacto Oracle líneas 216-230
    CASE
        WHEN TDD.TRX_SERVICE IN ('CASH_IN','DEPOSIT') THEN 'CREDIT'
        WHEN TDD.TRX_SERVICE IN ('CASH_OUT','CASH_OUT_ATM') THEN 'DEBIT'
        WHEN TDD.TRX_SERVICE IN ('TRANSFER','PAYMENT','EXTERNAL_PAYMENT','TRANSFER_TO_ANY_BANK_ACCOUNT') THEN 'TRANSFER'
        WHEN TDD.TRX_SERVICE IN ('FLOAT_TRANSFER','BATCH_TRANSFER','CUSTODY_ACCOUNTS_TRANSFER') THEN 'TRANSFER'
        WHEN TDD.TRX_SERVICE IN ('ADJUSTMENT') THEN 'ADJUSTMENT'
        WHEN TDD.TRX_SERVICE IN ('REVERSAL','REVERSAL_BATCH_TRANSFER','REFUND') THEN 'REFUND'
        ELSE 'TRANSFER'
    END AS "TransactionType",

    -- LÓGICA CONTEXT: Mapeo exacto Oracle líneas 231-245
    CASE
        WHEN TDD.TRX_SERVICE = 'CASH_IN' AND TDD.CANAL = 'AGENT' THEN 'agent-cashin'
        WHEN TDD.TRX_SERVICE = 'CASH_OUT' AND TDD.CANAL = 'AGENT' THEN 'agent-cashout'
        WHEN TDD.TRX_SERVICE = 'CASH_OUT_ATM' THEN 'atm-cashout'
        WHEN TDD.TRX_SERVICE IN ('TRANSFER','PAYMENT') THEN 'http-awspdp'
        WHEN TDD.TRX_SERVICE = 'EXTERNAL_PAYMENT' THEN 'http-xml_awspdp'
        WHEN TDD.TRX_SERVICE = 'TRANSFER_TO_ANY_BANK_ACCOUNT' THEN 'http-xml_awspdp'
        WHEN TDD.TRX_SERVICE IN ('FLOAT_TRANSFER','BATCH_TRANSFER','CUSTODY_ACCOUNTS_TRANSFER') THEN 'batch-awspdp'
        WHEN TDD.TRX_SERVICE = 'DEPOSIT' THEN 'batch-awspdp'
        WHEN TDD.TRX_SERVICE = 'ADJUSTMENT' THEN 'admin-awspdp'
        WHEN TDD.TRX_SERVICE IN ('REVERSAL','REVERSAL_BATCH_TRANSFER','REFUND') THEN 'admin-awspdp'
        ELSE 'http-awspdp'
    END AS "Context",

    -- LÓGICA COMMENT: Mapeo exacto Oracle líneas 246-253
    CASE
        WHEN TDD.TRX_SERVICE = 'CASH_IN' THEN 'Cash In'
        WHEN TDD.TRX_SERVICE = 'CASH_OUT' THEN 'Cash Out'
        WHEN TDD.TRX_SERVICE = 'CASH_OUT_ATM' THEN 'ATM Cash Out'
        WHEN TDD.TRX_SERVICE = 'TRANSFER' THEN 'Transfer'
        WHEN TDD.TRX_SERVICE = 'PAYMENT' THEN 'Payment'
        WHEN TDD.TRX_SERVICE = 'EXTERNAL_PAYMENT' THEN 'External Payment'
        WHEN TDD.TRX_SERVICE = 'TRANSFER_TO_ANY_BANK_ACCOUNT' THEN 'Transfer to Any Bank Account'
        WHEN TDD.TRX_SERVICE = 'FLOAT_TRANSFER' THEN 'Float Transfer'
        WHEN TDD.TRX_SERVICE = 'BATCH_TRANSFER' THEN 'Batch Transfer'
        WHEN TDD.TRX_SERVICE = 'CUSTODY_ACCOUNTS_TRANSFER' THEN 'Custody Accounts Transfer'
        WHEN TDD.TRX_SERVICE = 'DEPOSIT' THEN 'Deposit'
        WHEN TDD.TRX_SERVICE = 'ADJUSTMENT' THEN 'Adjustment'
        WHEN TDD.TRX_SERVICE = 'REVERSAL' THEN 'Reversal'
        WHEN TDD.TRX_SERVICE = 'REVERSAL_BATCH_TRANSFER' THEN 'Reversal Batch Transfer'
        WHEN TDD.TRX_SERVICE = 'REFUND' THEN 'Refund'
        ELSE TDD.TRX_SERVICE
    END AS "Comment",

    -- LÓGICA BANK_DOMAIN: Mapeo exacto Oracle usando bank_domain_accounts
    CASE
        WHEN TDD.PAYER_ISSUER_CODE = 'BNACION' THEN '{bank_domain_bnacion}'
        WHEN TDD.PAYER_ISSUER_CODE = 'CCUSCO' THEN '{bank_domain_ccusco}'
        WHEN TDD.PAYER_ISSUER_CODE = 'CRANDES' THEN '{bank_domain_crandes}'
        WHEN TDD.PAYER_ISSUER_CODE = '0231FCONFIANZA' THEN '{bank_domain_fconfianza}'
        WHEN TDD.PAYER_ISSUER_CODE = '0144QAPAQ' THEN '{bank_domain_qapaq}'
        WHEN TDD.PAYER_ISSUER_CODE = 'FCOMPARTAMOS' THEN '{bank_domain_fcompartamos}'
        ELSE NULL
    END AS "From_BankDomain",

    CASE
        WHEN TDD.PAYEE_ISSUER_CODE = 'BNACION' THEN '{bank_domain_bnacion}'
        WHEN TDD.PAYEE_ISSUER_CODE = 'CCUSCO' THEN '{bank_domain_ccusco}'
        WHEN TDD.PAYEE_ISSUER_CODE = 'CRANDES' THEN '{bank_domain_crandes}'
        WHEN TDD.PAYEE_ISSUER_CODE = '0231FCONFIANZA' THEN '{bank_domain_fconfianza}'
        WHEN TDD.PAYEE_ISSUER_CODE = '0144QAPAQ' THEN '{bank_domain_qapaq}'
        WHEN TDD.PAYEE_ISSUER_CODE = 'FCOMPARTAMOS' THEN '{bank_domain_fcompartamos}'
        ELSE NULL
    END AS "To_BankDomain",

    TDD.TRANSFER_DATE AS "TransferDate"

FROM TRX_DATA_DAY TDD
-- JOINs para obtener datos de USER_DATA
LEFT JOIN USER_DATA UD_PAYER ON TDD.PAYER_USER_ID = UD_PAYER.USER_ID
LEFT JOIN USER_DATA UD_PAYEE ON TDD.PAYEE_USER_ID = UD_PAYEE.USER_ID
-- JOINs para obtener WALLET_NUMBER más reciente (US.xxxxx)
LEFT JOIN MTX_WALLET_LATEST MW_PAYER ON TDD.PAYER_USER_ID = MW_PAYER.USER_ID AND MW_PAYER.RN = 1
LEFT JOIN MTX_WALLET_LATEST MW_PAYEE ON TDD.PAYEE_USER_ID = MW_PAYEE.USER_ID AND MW_PAYEE.RN = 1
-- JOINs especiales para caso 945661
LEFT JOIN MTX_WALLET_945661 MW945_DESC ON TDD.PAYER_USER_ID = MW945_DESC.USER_ID AND MW945_DESC.RN_DESC = 1
LEFT JOIN MTX_WALLET_945661 MW945_ASC ON TDD.PAYER_USER_ID = MW945_ASC.USER_ID AND MW945_ASC.RN_ASC = 1

ORDER BY TDD.TRANSFER_DATE, TDD.TRANSFER_ID
