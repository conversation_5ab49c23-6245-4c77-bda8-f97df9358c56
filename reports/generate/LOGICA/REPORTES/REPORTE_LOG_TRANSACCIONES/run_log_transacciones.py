#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
========================================================================
REPORTE_LOG_TRANSACCIONES - CODE NINJA MASTER
Estructura CORRECTA: Archivos SQL separados + Runner Python
Replica EXACTAMENTE el pipeline original pero ORGANIZADO
========================================================================
"""

import logging
import duckdb
import boto3
from pathlib import Path
from datetime import datetime
from typing import Dict, Any
import configparser

class LogTransaccionesRunner:
    """
    Runner que ejecuta cada archivo SQL en orden secuencial
    Mantiene la misma cantidad de funciones que el original
    Cada función llama a su archivo SQL correspondiente
    """
    
    def __init__(self, config_path: str):
        self.config = configparser.ConfigParser()
        self.config.read(config_path)
        self.conn = duckdb.connect()
        self.logger = self._setup_logger()

        # Configurar S3 en DuckDB
        self._setup_s3_connection()
        
        # Construir rutas S3 desde configuración
        self.base_paths = {}

        # Procesar fuentes S3
        for source_name, source_value in self.config.items('s3_sources'):
            parts = [p.strip() for p in source_value.split(',')]
            if len(parts) >= 2:
                bucket = parts[0]
                prefix = parts[1]
                # Para tablas estáticas usar consolidado_puro.parquet
                if source_name in ['mtx_wallet', 'user_profile', 'sys_service_types', 'channel_grades',
                                 'marketing_profile', 'mtx_categories', 'issuer_details', 'sys_service_provider', 'user_accounts']:
                    self.base_paths[f'{source_name}_path'] = f's3://{bucket}/{prefix}/consolidado_puro.parquet'
                # Para archivos específicos (USER_DATA_TRX.parquet, USER_ACCOUNT_HISTORY.parquet)
                elif source_name in ['user_data_trx', 'user_account_history']:
                    self.base_paths[f'{source_name}_path'] = f's3://{bucket}/{prefix}'
                else:
                    # Para tablas particionadas, se agregará la fecha dinámicamente
                    self.base_paths[f'{source_name}_path'] = f's3://{bucket}/{prefix}'

        # Directorios de trabajo
        self.base_paths['temp_dir'] = self.config.get('pipeline_config', 'temp_dir', fallback='TEMP_LOGS_TRANSACCIONES')
        self.base_paths['output_dir'] = self.config.get('pipeline_config', 'output_dir_csv', fallback='output')
        
    def _setup_logger(self) -> logging.Logger:
        """Configurar logger"""
        logger = logging.getLogger('LogTransacciones')
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger

    def _setup_s3_connection(self):
        """Configurar conexión S3 en DuckDB"""
        try:
            # Obtener credenciales activas de AWS CLI
            session = boto3.Session()
            credentials = session.get_credentials().get_frozen_credentials()

            # Cargar soporte para S3 en DuckDB
            self.conn.sql("INSTALL httpfs;")
            self.conn.sql("LOAD httpfs;")
            self.conn.sql("SET s3_region='us-east-1';")
            self.conn.sql("SET s3_use_ssl=true;")
            self.conn.sql("SET s3_url_style='path';")

            # Pasar credenciales explícitamente
            self.conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
            self.conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
            if credentials.token:
                self.conn.sql(f"SET s3_session_token='{credentials.token}';")

            self.logger.info("Conexión S3 configurada exitosamente")

        except Exception as e:
            self.logger.error(f"Error configurando conexión S3: {e}")
            raise
    
    def execute_sql_file(self, sql_file: str, output_path: str, variables: Dict[str, Any]) -> str:
        """
        Ejecuta un archivo SQL y guarda el resultado en parquet
        """
        try:
            # Leer archivo SQL
            sql_path = Path(__file__).parent / 'queries' / 'ctes' / sql_file
            with open(sql_path, 'r', encoding='utf-8') as f:
                query_content = f.read()
            
            # Formatear query con variables
            formatted_query = query_content.format(**variables)
            
            # Ejecutar y guardar
            copy_query = f"COPY ({formatted_query}) TO '{output_path}' (FORMAT PARQUET);"
            self.conn.execute(copy_query)
            
            # Verificar registros
            count_result = self.conn.execute(f"SELECT COUNT(*) FROM read_parquet('{output_path}')").fetchone()
            record_count = count_result[0] if count_result else 0
            
            self.logger.info(f"{sql_file} completado: {record_count:,} registros -> {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"Error ejecutando {sql_file}: {e}")
            raise
    
    def _build_variables(self, fecha: str, date_folder: str) -> Dict[str, Any]:
        """Construye variables dinámicas para las queries"""
        variables = {**self.base_paths, 'fecha_inicio': fecha, 'date_folder': date_folder}

        # Agregar rutas con fecha para tablas particionadas
        fecha_parts = fecha.split('-')
        year, month, day = fecha_parts[0], fecha_parts[1], fecha_parts[2]

        # Tablas particionadas por fecha (solo las que realmente están particionadas)
        partitioned_tables = ['mtx_transaction_header', 'mtx_transaction_items']
        for table in partitioned_tables:
            if f'{table}_path' in self.base_paths:
                base_path = self.base_paths[f'{table}_path']
                variables[f'{table}_path'] = f'{base_path}/{year}/{month}/{day}/*'

        return variables

    def execute_trx_header(self, fecha: str, date_folder: str) -> str:
        """Función 1: TRX_HEADER - Replica función original"""
        variables = self._build_variables(fecha, date_folder)
        output_path = f"{self.base_paths['temp_dir']}/{date_folder}/TRX_HEADER.parquet"
        Path(f"{self.base_paths['temp_dir']}/{date_folder}").mkdir(parents=True, exist_ok=True)
        return self.execute_sql_file('01_trx_header.sql', output_path, variables)
    
    def execute_trx_items(self, fecha: str, date_folder: str) -> str:
        """Función 2: TRX_ITEMS - Replica función original"""
        variables = self._build_variables(fecha, date_folder)
        output_path = f"{self.base_paths['temp_dir']}/{date_folder}/TRX_ITEMS.parquet"
        return self.execute_sql_file('02_trx_items.sql', output_path, variables)

    def execute_mtx_wallet_latest(self, fecha: str, date_folder: str) -> str:
        """Función 3: MTX_WALLET_LATEST - Replica función original"""
        variables = self._build_variables(fecha, date_folder)
        output_path = f"{self.base_paths['temp_dir']}/{date_folder}/MTX_WALLET_LATEST.parquet"
        return self.execute_sql_file('03_mtx_wallet_latest.sql', output_path, variables)

    def execute_mtx_wallet_945661(self, fecha: str, date_folder: str) -> str:
        """Función 4: MTX_WALLET_945661 - Replica función original"""
        variables = self._build_variables(fecha, date_folder)
        output_path = f"{self.base_paths['temp_dir']}/{date_folder}/MTX_WALLET_945661.parquet"
        return self.execute_sql_file('04_mtx_wallet_945661.sql', output_path, variables)

    def execute_wallets(self, fecha: str, date_folder: str) -> str:
        """Función 5: WALLETS - Replica función original"""
        variables = self._build_variables(fecha, date_folder)
        output_path = f"{self.base_paths['temp_dir']}/{date_folder}/WALLETS.parquet"
        return self.execute_sql_file('05_wallets.sql', output_path, variables)

    def execute_user_data(self, fecha: str, date_folder: str) -> str:
        """Función 6: USER_DATA - Replica función original"""
        variables = self._build_variables(fecha, date_folder)
        output_path = f"{self.base_paths['temp_dir']}/{date_folder}/USER_DATA.parquet"
        return self.execute_sql_file('06_user_data.sql', output_path, variables)

    def execute_mti_scp(self, fecha: str, date_folder: str) -> str:
        """Función 7: MTI_SCP - Replica función original"""
        variables = self._build_variables(fecha, date_folder)
        output_path = f"{self.base_paths['temp_dir']}/{date_folder}/MTI_SCP.parquet"
        return self.execute_sql_file('07_mti_scp.sql', output_path, variables)

    def execute_wallets_grade(self, fecha: str, date_folder: str) -> str:
        """Función 8: WALLETS_GRADE - Replica función original"""
        variables = self._build_variables(fecha, date_folder)
        output_path = f"{self.base_paths['temp_dir']}/{date_folder}/WALLETS_GRADE.parquet"
        return self.execute_sql_file('08_wallets_grade.sql', output_path, variables)

    def execute_mti_mp(self, fecha: str, date_folder: str) -> str:
        """Función 9: MTI_MP - Replica función original"""
        variables = self._build_variables(fecha, date_folder)
        output_path = f"{self.base_paths['temp_dir']}/{date_folder}/MTI_MP.parquet"
        return self.execute_sql_file('09_mti_mp.sql', output_path, variables)

    def execute_mti_mr(self, fecha: str, date_folder: str) -> str:
        """Función 10: MTI_MR - Replica función original"""
        variables = self._build_variables(fecha, date_folder)
        output_path = f"{self.base_paths['temp_dir']}/{date_folder}/MTI_MR.parquet"
        return self.execute_sql_file('10_mti_mr.sql', output_path, variables)

    def execute_reversal(self, fecha: str, date_folder: str) -> str:
        """Función 11: REVERSAL - Replica función original"""
        variables = self._build_variables(fecha, date_folder)
        output_path = f"{self.base_paths['temp_dir']}/{date_folder}/REVERSAL.parquet"
        return self.execute_sql_file('11_reversal.sql', output_path, variables)

    def execute_trx_data_day(self, fecha: str, date_folder: str) -> str:
        """Función 12: TRX_DATA_DAY - Replica función original"""
        variables = self._build_variables(fecha, date_folder)
        output_path = f"{self.base_paths['temp_dir']}/{date_folder}/TRX_DATA_DAY.parquet"
        return self.execute_sql_file('12_trx_data_day.sql', output_path, variables)

    def execute_deduplicated_data(self, fecha: str, date_folder: str) -> str:
        """Función 13: DEDUPLICATED_DATA - Replica función original"""
        variables = self._build_variables(fecha, date_folder)
        output_path = f"{self.base_paths['temp_dir']}/{date_folder}/PRE_LOG_TRX.parquet"

        # Ejecutar DEDUPLICATED_DATA y aplicar deduplicación
        temp_output = f"{self.base_paths['temp_dir']}/{date_folder}/DEDUPLICATED_DATA_TEMP.parquet"
        self.execute_sql_file('13_deduplicated_data.sql', temp_output, variables)

        # Aplicar deduplicación final
        dedup_query = f"""
        SELECT * EXCLUDE (rn)
        FROM read_parquet('{temp_output}', union_by_name=true)
        WHERE rn = 1
        """
        copy_query = f"COPY ({dedup_query}) TO '{output_path}' (FORMAT PARQUET);"
        self.conn.execute(copy_query)

        # Limpiar archivo temporal
        Path(temp_output).unlink()

        # Verificar registros finales
        count_result = self.conn.execute(f"SELECT COUNT(*) FROM read_parquet('{output_path}')").fetchone()
        record_count = count_result[0] if count_result else 0
        self.logger.info(f"PRE_LOG_TRX completado con deduplicación: {record_count:,} registros")

        return output_path

    def execute_sp_pre_log_trx_update(self, fecha: str, date_folder: str) -> str:
        """Función 14: SP_PRE_LOG_TRX_UPDATE - Replica función original"""
        try:
            # Verificar si hay casos que requieren actualización
            pre_log_path = f"{self.base_paths['temp_dir']}/{date_folder}/PRE_LOG_TRX.parquet"
            casos_niubiz = self.conn.execute(f"""
                SELECT COUNT(*)
                FROM read_parquet('{pre_log_path}', union_by_name=true)
                WHERE "TransactionType" = 'REVERSAL'
                AND "Context" = 'internal'
                AND ("From_Msisdn" = '51991022660' OR "To_Msisdn" = '51991022660')
            """).fetchone()[0]

            if casos_niubiz > 0:
                self.logger.info(f"Encontrados {casos_niubiz} casos Niubiz para actualizar")

                # Hacer backup
                backup_path = f"{self.base_paths['temp_dir']}/{date_folder}/PRE_LOG_TRX_ORIGINAL.parquet"
                self.conn.execute(f"""
                    COPY (SELECT * FROM read_parquet('{pre_log_path}'))
                    TO '{backup_path}' (FORMAT PARQUET)
                """)

                # Ejecutar actualización (usando query inline por simplicidad)
                update_query = f"""
                WITH updated_data AS (
                    SELECT
                        PLT.*,
                        CASE
                            WHEN PLT."TransactionType" = 'REVERSAL'
                            AND PLT."Context" = 'internal'
                            AND (PLT."From_Msisdn" = '51991022660' OR PLT."To_Msisdn" = '51991022660')
                            THEN 'http-fcompartamos_niubiz_interope'
                            ELSE PLT."Context"
                        END AS "Context_Updated"
                    FROM read_parquet('{pre_log_path}', union_by_name=true) PLT
                )
                SELECT * EXCLUDE ("Context") RENAME ("Context_Updated" AS "Context")
                FROM updated_data
                """

                copy_query = f"COPY ({update_query}) TO '{pre_log_path}' (FORMAT PARQUET);"
                self.conn.execute(copy_query)

                self.logger.info(f"SP_PRE_LOG_TRX_UPDATE completado - Backup: {backup_path}")
            else:
                self.logger.info("No hay casos Niubiz para actualizar")

            return pre_log_path

        except Exception as e:
            self.logger.warning(f"SP_PRE_LOG_TRX_UPDATE falló (no crítico): {e}")
            return f"{self.base_paths['temp_dir']}/{date_folder}/PRE_LOG_TRX.parquet"

    def execute_sp_log_trx(self, fecha: str, date_folder: str) -> str:
        """Función 15: SP_LOG_TRX - Replica función original"""
        variables = self._build_variables(fecha, date_folder)
        output_path = f"{self.base_paths['temp_dir']}/{date_folder}/LOG_TRX_FINAL.parquet"
        return self.execute_sql_file('14_sp_log_trx.sql', output_path, variables)

    def execute_extract_final_csv(self, fecha: str, date_folder: str) -> str:
        """Función 16: EXTRACT_FINAL_CSV - Replica función original"""
        variables = self._build_variables(fecha, date_folder)

        # Leer archivo SQL
        sql_path = Path(__file__).parent / 'queries' / 'ctes' / '15_extract_final_csv.sql'
        with open(sql_path, 'r', encoding='utf-8') as f:
            query_content = f.read()

        # Formatear query
        formatted_query = query_content.format(**variables)

        # Generar CSV
        fecha_formatted = fecha.replace('-', '')
        csv_filename = f"TR-{fecha_formatted}.csv"
        csv_path = f"{self.base_paths['output_dir']}/{csv_filename}"

        copy_query = f"COPY ({formatted_query}) TO '{csv_path}' (FORMAT CSV, HEADER true);"
        self.conn.execute(copy_query)

        # Verificar registros
        count_result = self.conn.execute(f"SELECT COUNT(*) FROM ({formatted_query})").fetchone()
        record_count = count_result[0] if count_result else 0
        self.logger.info(f"CSV final generado: {record_count:,} registros -> {csv_path}")

        return csv_path

    def run_pipeline(self, fecha: str) -> dict:
        """
        Ejecuta el pipeline completo en orden secuencial
        Replica EXACTAMENTE el flujo original pero ORGANIZADO
        """
        try:
            self.logger.info(f"🚀 Iniciando pipeline LOG_TRANSACCIONES ORGANIZADO para fecha: {fecha}")
            start_time = datetime.now()

            date_folder = fecha.replace('-', '')

            results = {
                'fecha': fecha,
                'inicio': start_time.isoformat(),
                'etapas': {},
                'archivos_generados': []
            }

            # PASO 1-11: Ejecutar CTEs en orden secuencial
            self.logger.info("PASO 1-11: Ejecutando CTEs en orden...")

            # 1. TRX_HEADER
            self.execute_trx_header(fecha, date_folder)

            # 2. TRX_ITEMS
            self.execute_trx_items(fecha, date_folder)

            # 3. MTX_WALLET_LATEST
            self.execute_mtx_wallet_latest(fecha, date_folder)

            # 4. MTX_WALLET_945661
            self.execute_mtx_wallet_945661(fecha, date_folder)

            # 5. WALLETS
            self.execute_wallets(fecha, date_folder)

            # 6. USER_DATA
            self.execute_user_data(fecha, date_folder)

            # 7. MTI_SCP
            self.execute_mti_scp(fecha, date_folder)

            # 8. WALLETS_GRADE
            self.execute_wallets_grade(fecha, date_folder)

            # 9. MTI_MP
            self.execute_mti_mp(fecha, date_folder)

            # 10. MTI_MR
            self.execute_mti_mr(fecha, date_folder)

            # 11. REVERSAL
            self.execute_reversal(fecha, date_folder)

            # 12. TRX_DATA_DAY
            self.execute_trx_data_day(fecha, date_folder)

            # 13. DEDUPLICATED_DATA (genera PRE_LOG_TRX)
            pre_log_path = self.execute_deduplicated_data(fecha, date_folder)
            results['archivos_generados'].append(pre_log_path)

            # 14. SP_PRE_LOG_TRX_UPDATE
            self.execute_sp_pre_log_trx_update(fecha, date_folder)

            # 15. SP_LOG_TRX
            log_trx_path = self.execute_sp_log_trx(fecha, date_folder)
            results['archivos_generados'].append(log_trx_path)

            # 16. EXTRACT_FINAL_CSV
            csv_path = self.execute_extract_final_csv(fecha, date_folder)
            results['archivos_generados'].append(csv_path)

            # Finalizar
            end_time = datetime.now()
            duration = end_time - start_time

            results.update({
                'fin': end_time.isoformat(),
                'duracion_segundos': duration.total_seconds(),
                'estado': 'COMPLETADO',
                'csv_final': csv_path
            })

            self.logger.info(f"✅ Pipeline completado exitosamente en {duration.total_seconds():.1f}s")
            self.logger.info(f"📄 CSV final: {csv_path}")

            return results

        except Exception as e:
            self.logger.error(f"❌ Error en pipeline: {e}")
            results.update({
                'fin': datetime.now().isoformat(),
                'estado': 'ERROR',
                'error': str(e)
            })
            raise

def main():
    """Función principal para ejecutar desde línea de comandos"""
    import sys

    if len(sys.argv) != 2:
        print("Uso: python run_log_transacciones.py YYYY-MM-DD")
        sys.exit(1)

    fecha = sys.argv[1]
    config_path = Path(__file__).parent.parent.parent / 'CONFIGURACIONES' / 'REPORTE_LOG_TRANSACCIONES' / 'config.ini'

    runner = LogTransaccionesRunner(str(config_path))
    results = runner.run_pipeline(fecha)

    print(f"Pipeline completado: {results['estado']}")
    if results['estado'] == 'COMPLETADO':
        print(f"CSV generado: {results['csv_final']}")

if __name__ == "__main__":
    main()
