#!/usr/bin/env python3
"""
Runner personalizado para REPORTE_LOG_TRANSACCIONES
Extiende el framework LOGICA con lógica específica del pipeline LOG_TRANSACCIONES
Migrado del pipeline_log_transacciones_duckdb.py
"""

import sys
import os
import json
import duckdb
import boto3
import logging
import configparser
from datetime import datetime, timedelta
from pathlib import Path

# Agregar el directorio padre al path para importar módulos LOGICA
sys.path.append(str(Path(__file__).parent.parent.parent))

# Importar post-procesamiento específico
from REPORTES.REPORTE_LOG_TRANSACCIONES.post_process import (
    post_process_log_transacciones,
    setup_bank_domain_mappings,
    setup_special_users
)

class LogTransaccionesRunner:
    """
    Runner personalizado para el pipeline LOG_TRANSACCIONES
    Combina el framework LOGICA con la lógica específica del pipeline original
    """
    
    def __init__(self, config_path):
        self.config_path = config_path
        self.config = configparser.ConfigParser()
        self.config.read(config_path)
        
        self.setup_logging()
        self.conn = duckdb.connect()
        self.setup_s3_credentials()
        self.setup_directories()
        
        # Configuración específica del pipeline
        self.bank_domain_accounts = setup_bank_domain_mappings(self.config)
        self.special_users = setup_special_users(self.config)
        
    def setup_logging(self):
        """Configura el sistema de logging"""
        log_dir = self.config.get('general', 'log_dir', fallback='logs')
        Path(log_dir).mkdir(parents=True, exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(f'{log_dir}/log_transacciones_runner.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('LogTransaccionesRunner')
        
    def setup_s3_credentials(self):
        """Configura credenciales S3 en DuckDB"""
        try:
            self.logger.info("Configurando credenciales S3...")
            
            # Obtener credenciales activas de AWS CLI
            session = boto3.Session()
            credentials = session.get_credentials().get_frozen_credentials()
            
            # Cargar soporte para S3 en DuckDB
            self.conn.sql("INSTALL httpfs;")
            self.conn.sql("LOAD httpfs;")
            self.conn.sql("SET s3_region='us-east-1';")
            self.conn.sql("SET s3_use_ssl=true;")
            self.conn.sql("SET s3_url_style='path';")
            
            # Pasar credenciales explícitamente
            self.conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
            self.conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
            if credentials.token:
                self.conn.sql(f"SET s3_session_token='{credentials.token}';")
                
            self.logger.info("Credenciales S3 configuradas exitosamente")
            
        except Exception as e:
            self.logger.error(f"Error configurando credenciales S3: {e}")
            raise
            
    def setup_directories(self):
        """Crea directorios necesarios"""
        output_dir = self.config.get('general', 'output_dir', fallback='S3_LOG_TRANSACCIONES_OUTPUT')
        log_dir = self.config.get('general', 'log_dir', fallback='logs')
        temp_dir = self.config.get('pipeline_config', 'temp_dir', fallback='TEMP_LOGS_TRANSACCIONES')
        output_dir_csv = self.config.get('pipeline_config', 'output_dir_csv', fallback='output')
        
        directories = [output_dir, log_dir, temp_dir, output_dir_csv]
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
            
    def build_s3_paths(self, fecha):
        """
        Construye las rutas S3 dinámicamente para la fecha específica
        Replica la lógica del pipeline original
        """
        year = fecha[:4]
        month = fecha[5:7]
        day = fecha[8:10]
        
        s3_paths = {}
        
        # Procesar cada fuente S3 de la configuración
        for source_name, source_value in self.config.items('s3_sources'):
            parts = [p.strip() for p in source_value.split(',')]
            if len(parts) >= 2:
                bucket = parts[0]
                prefix = parts[1]
                region = parts[2] if len(parts) > 2 else 'us-east-1'
                
                # Determinar si es tabla particionada o estática
                if source_name in ['mtx_transaction_header', 'mtx_transaction_items']:
                    # Tablas particionadas por fecha
                    path = f's3://{bucket}/{prefix}/{year}/{month}/{day}/*.parquet'
                elif source_name in ['user_data_trx', 'user_account_history']:
                    # Tablas en Golden Zone
                    if source_name == 'user_data_trx':
                        path = f's3://{bucket}/{prefix}/USER_DATA_TRX.parquet'
                    else:
                        path = f's3://{bucket}/{prefix}/USER_ACCOUNT_HISTORY.parquet'
                else:
                    # Tablas estáticas con consolidado_puro.parquet
                    path = f's3://{bucket}/{prefix}/consolidado_puro.parquet'
                
                s3_paths[f'{source_name}_path'] = path
                s3_paths[f'{source_name}_bucket'] = bucket
                s3_paths[f'{source_name}_prefix'] = prefix
                s3_paths[f'{source_name}_region'] = region
                
        return s3_paths
        
    def prepare_query_variables(self, fecha, s3_paths):
        """
        Prepara todas las variables necesarias para los queries SQL
        """
        date_folder = fecha.replace('-', '')
        temp_dir = self.config.get('pipeline_config', 'temp_dir', fallback='TEMP_LOGS_TRANSACCIONES')
        
        # Variables base
        variables = {
            'fecha_inicio': fecha,
            'date_folder': date_folder,
            'temp_dir': temp_dir,
            # Bank domain mappings
            'bank_domain_bnacion': self.bank_domain_accounts.get('BNACION', '1334853'),
            'bank_domain_ccusco': self.bank_domain_accounts.get('CCUSCO', '1464437'),
            'bank_domain_crandes': self.bank_domain_accounts.get('CRANDES', '1414519'),
            'bank_domain_fconfianza': self.bank_domain_accounts.get('0231FCONFIANZA', '1882233'),
            'bank_domain_qapaq': self.bank_domain_accounts.get('0144QAPAQ', '1131834'),
            'bank_domain_fcompartamos': self.bank_domain_accounts.get('FCOMPARTAMOS', '1188057'),
            # Special users tuple para SQL IN clause
            'special_users_tuple': str(tuple(self.special_users)).replace("'", "''")
        }
        
        # Agregar rutas S3
        variables.update(s3_paths)
        
        return variables
        
    def execute_query(self, query_name, query_content, variables):
        """
        Ejecuta un query SQL con las variables proporcionadas
        """
        try:
            self.logger.info(f"Ejecutando query: {query_name}")
            
            # Reemplazar variables en el query
            formatted_query = query_content.format(**variables)
            
            # Manejar queries especiales que no retornan resultados
            if query_name in ['sp_pre_log_trx_update']:
                # Queries de actualización - ejecutar directamente
                self.conn.execute(formatted_query)
                self.logger.info(f"Query {query_name} ejecutado (actualización)")
                return None
            elif query_name == 'sp_pre_log_trx':
                # SP_PRE_LOG_TRX - guardar resultado en archivo
                output_path = f"{variables['temp_dir']}/{variables['date_folder']}/PRE_LOG_TRX.parquet"
                Path(f"{variables['temp_dir']}/{variables['date_folder']}").mkdir(parents=True, exist_ok=True)
                
                copy_query = f"COPY ({formatted_query}) TO '{output_path}' (FORMAT PARQUET);"
                self.conn.execute(copy_query)
                
                # Verificar registros
                count_result = self.conn.execute(f"SELECT COUNT(*) FROM read_parquet('{output_path}')").fetchone()
                record_count = count_result[0] if count_result else 0
                self.logger.info(f"SP_PRE_LOG_TRX completado: {record_count} registros -> {output_path}")
                return output_path
                
            elif query_name == 'sp_log_trx':
                # SP_LOG_TRX - guardar resultado en archivo
                output_path = f"{variables['temp_dir']}/{variables['date_folder']}/LOG_TRX_FINAL.parquet"
                
                copy_query = f"COPY ({formatted_query}) TO '{output_path}' (FORMAT PARQUET);"
                self.conn.execute(copy_query)
                
                # Verificar registros
                count_result = self.conn.execute(f"SELECT COUNT(*) FROM read_parquet('{output_path}')").fetchone()
                record_count = count_result[0] if count_result else 0
                self.logger.info(f"SP_LOG_TRX completado: {record_count} registros -> {output_path}")
                return output_path
                
            elif query_name == 'extract_final_csv':
                # Extract final CSV - retornar resultado para post-procesamiento
                result = self.conn.execute(formatted_query).fetchall()
                columns = [desc[0] for desc in self.conn.description]
                self.logger.info(f"Extract final CSV completado: {len(result)} registros")
                return {'data': result, 'columns': columns}
                
            else:
                # Query genérico - retornar resultado
                result = self.conn.execute(formatted_query).fetchall()
                columns = [desc[0] for desc in self.conn.description]
                return {'data': result, 'columns': columns}
                
        except Exception as e:
            self.logger.error(f"Error ejecutando query {query_name}: {e}")
            raise
            
    def run_pipeline(self, fecha):
        """
        Ejecuta el pipeline completo para una fecha específica
        """
        try:
            self.logger.info(f"🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: {fecha}")
            start_time = datetime.now()
            
            results = {
                'fecha': fecha,
                'inicio': start_time.isoformat(),
                'etapas': {},
                'archivos_generados': [],
                'errores': []
            }
            
            # PASO 1: Construir rutas S3 dinámicas
            s3_paths = self.build_s3_paths(fecha)
            variables = self.prepare_query_variables(fecha, s3_paths)
            
            # PASO 2: Ejecutar queries en orden
            query_list = self.config.get('queries', 'query_list').split(', ')
            
            for query_name in query_list:
                try:
                    etapa_start = datetime.now()
                    
                    # Leer archivo de query
                    query_file = Path(__file__).parent / 'queries' / f'{query_name}.sql'
                    if not query_file.exists():
                        raise FileNotFoundError(f"Query file not found: {query_file}")
                        
                    with open(query_file, 'r', encoding='utf-8') as f:
                        query_content = f.read()
                    
                    # Ejecutar query
                    query_result = self.execute_query(query_name, query_content, variables)
                    
                    etapa_end = datetime.now()
                    etapa_duration = (etapa_end - etapa_start).total_seconds()
                    
                    results['etapas'][query_name] = {
                        'estado': 'COMPLETADO',
                        'duracion_segundos': etapa_duration,
                        'resultado': str(query_result) if query_result else 'N/A'
                    }
                    
                    if isinstance(query_result, str) and query_result.endswith('.parquet'):
                        results['archivos_generados'].append(query_result)
                        
                    self.logger.info(f"Etapa {query_name} completada en {etapa_duration:.2f}s")
                    
                except Exception as e:
                    error_msg = f"Error en etapa {query_name}: {e}"
                    self.logger.error(error_msg)
                    results['errores'].append(error_msg)
                    results['etapas'][query_name] = {
                        'estado': 'ERROR',
                        'error': str(e)
                    }
                    raise
            
            # PASO 3: Post-procesamiento
            try:
                self.logger.info("Iniciando post-procesamiento...")
                post_start = datetime.now()
                
                post_results = post_process_log_transacciones(fecha, self.config, self.conn, self.logger)
                
                post_end = datetime.now()
                post_duration = (post_end - post_start).total_seconds()
                
                results['etapas']['post_procesamiento'] = {
                    'estado': 'COMPLETADO',
                    'duracion_segundos': post_duration,
                    'detalles': post_results
                }
                
                results['archivos_generados'].extend(post_results.get('archivos_generados', []))
                
            except Exception as e:
                error_msg = f"Error en post-procesamiento: {e}"
                self.logger.error(error_msg)
                results['errores'].append(error_msg)
                results['etapas']['post_procesamiento'] = {
                    'estado': 'ERROR',
                    'error': str(e)
                }
                # No hacer raise aquí - el post-procesamiento es opcional
                
            # PASO 4: Resumen final
            end_time = datetime.now()
            total_duration = (end_time - start_time).total_seconds()
            
            results['fin'] = end_time.isoformat()
            results['duracion_total_segundos'] = total_duration
            results['estado'] = 'COMPLETADO' if not results['errores'] else 'COMPLETADO_CON_ERRORES'
            
            self.logger.info(f"✅ Pipeline LOG_TRANSACCIONES completado en {total_duration:.2f}s")
            self.logger.info(f"📁 Archivos generados: {len(results['archivos_generados'])}")
            
            if results['errores']:
                self.logger.warning(f"⚠️  Se encontraron {len(results['errores'])} errores")
                
            return results
            
        except Exception as e:
            self.logger.error(f"❌ Error crítico en pipeline: {e}")
            results['estado'] = 'ERROR'
            results['error_critico'] = str(e)
            raise

def main():
    """Función principal"""
    if len(sys.argv) < 2:
        print("Uso: python run_log_transacciones.py [FECHA]")
        print("FECHA: Formato YYYY-MM-DD (opcional, por defecto ayer)")
        sys.exit(1)
        
    # Determinar fecha
    if len(sys.argv) >= 2:
        fecha = sys.argv[1]
        try:
            datetime.strptime(fecha, '%Y-%m-%d')
        except ValueError:
            print(f"Error: Formato de fecha inválido '{fecha}'. Use YYYY-MM-DD")
            sys.exit(1)
    else:
        fecha = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        
    # Ruta de configuración
    config_path = Path(__file__).parent.parent.parent / 'CONFIGURACIONES' / 'REPORTE_LOG_TRANSACCIONES' / 'config.ini'
    
    if not config_path.exists():
        print(f"Error: Archivo de configuración no encontrado: {config_path}")
        sys.exit(1)
        
    print(f"🚀 Iniciando Pipeline LOG_TRANSACCIONES Modernizado")
    print(f"📅 Fecha de procesamiento: {fecha}")
    print(f"🏗️  Arquitectura: Framework LOGICA + DuckDB + Parquet (S3)")
    print("=" * 80)
    
    try:
        # Ejecutar pipeline
        runner = LogTransaccionesRunner(str(config_path))
        results = runner.run_pipeline(fecha)
        
        # Mostrar resumen
        print("\n" + "=" * 80)
        print("📊 RESUMEN DE EJECUCIÓN")
        print("=" * 80)
        print(f"Estado: {results['estado']}")
        print(f"Duración total: {results['duracion_total_segundos']:.2f} segundos")
        print(f"Archivos generados: {len(results['archivos_generados'])}")
        
        if results['archivos_generados']:
            print("\n📁 Archivos generados:")
            for archivo in results['archivos_generados']:
                print(f"  - {archivo}")
                
        if results['errores']:
            print(f"\n⚠️  Errores encontrados: {len(results['errores'])}")
            for error in results['errores']:
                print(f"  - {error}")
                
        print("\n✅ Pipeline completado exitosamente!")
        
    except Exception as e:
        print(f"\n❌ Error crítico: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
