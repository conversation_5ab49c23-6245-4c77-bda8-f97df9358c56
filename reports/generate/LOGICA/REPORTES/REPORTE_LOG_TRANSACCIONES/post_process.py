#!/usr/bin/env python3
"""
Post-procesamiento para REPORTE_LOG_TRANSACCIONES
Migrado del pipeline_log_transacciones_duckdb.py
Maneja lógica específica que no se puede hacer en SQL puro
"""

import duckdb
import json
import logging
from pathlib import Path
from datetime import datetime

def post_process_log_transacciones(fecha, config, conn, logger):
    """
    Post-procesa los resultados del pipeline LOG_TRANSACCIONES
    
    Args:
        fecha (str): Fecha en formato YYYY-MM-DD
        config (ConfigParser): Configuración del reporte
        conn (duckdb.DuckDBPyConnection): Conexión DuckDB
        logger (logging.Logger): Logger para mensajes
    
    Returns:
        dict: Resultados del post-procesamiento
    """
    try:
        logger.info(f"Iniciando post-procesamiento LOG_TRANSACCIONES para fecha: {fecha}")
        
        # Obtener configuración
        temp_dir = config.get('pipeline_config', 'temp_dir', fallback='TEMP_LOGS_TRANSACCIONES')
        output_dir_csv = config.get('pipeline_config', 'output_dir_csv', fallback='output')
        
        # Preparar directorios
        date_folder = fecha.replace('-', '')
        temp_path = Path(temp_dir) / date_folder
        output_path = Path(output_dir_csv)
        
        # Crear directorios si no existen
        temp_path.mkdir(parents=True, exist_ok=True)
        output_path.mkdir(parents=True, exist_ok=True)
        
        results = {
            'fecha': fecha,
            'post_procesamiento': {},
            'archivos_generados': [],
            'errores': []
        }
        
        # PASO 1: Verificar que LOG_TRX_FINAL.parquet existe
        log_trx_final_path = temp_path / "LOG_TRX_FINAL.parquet"
        if not log_trx_final_path.exists():
            raise FileNotFoundError(f"No se encontró LOG_TRX_FINAL.parquet en {log_trx_final_path}")
        
        # PASO 2: Generar CSV final TR-{YYYYMMDD}.csv
        logger.info("Generando CSV final...")
        
        # Ejecutar query de extracción final (ya ejecutada en el pipeline principal)
        # Aquí solo necesitamos convertir el resultado a CSV
        
        csv_filename = f"TR-{date_folder}.csv"
        csv_path = output_path / csv_filename
        
        # Leer el resultado de extract_final_csv y guardarlo como CSV
        # Nota: En el framework LOGICA, el resultado de extract_final_csv 
        # ya está disponible como el último resultado del pipeline
        
        # Por ahora, simplemente copiamos LOG_TRX_FINAL como CSV
        copy_query = f"""
        COPY (
            SELECT *
            FROM read_parquet('{log_trx_final_path}', union_by_name=true)
            WHERE CAST("DateTime" AS DATE) = CAST('{fecha}' AS DATE)
            ORDER BY "DateTime", "TransferID"
        ) TO '{csv_path}' (FORMAT CSV, HEADER);
        """
        
        conn.execute(copy_query)
        
        # Verificar registros en CSV
        count_result = conn.execute(f"""
            SELECT COUNT(*) FROM read_csv('{csv_path}', AUTO_DETECT=true)
        """).fetchone()
        record_count = count_result[0] if count_result else 0
        
        results['post_procesamiento']['csv_final'] = {
            'archivo': str(csv_path),
            'registros': record_count,
            'estado': 'COMPLETADO'
        }
        results['archivos_generados'].append(str(csv_path))
        
        logger.info(f"CSV final generado: {csv_path} ({record_count} registros)")
        
        # PASO 3: Generar estadísticas de resumen
        logger.info("Generando estadísticas de resumen...")
        
        stats_query = f"""
        SELECT
            COUNT(*) as total_transacciones,
            COUNT(DISTINCT "FromID_Mobiquity") as usuarios_unicos_payer,
            COUNT(DISTINCT "ToID_Mobiquity") as usuarios_unicos_payee,
            SUM("Amount") as monto_total,
            COUNT(DISTINCT "TransactionType") as tipos_transaccion,
            MIN("DateTime") as primera_transaccion,
            MAX("DateTime") as ultima_transaccion
        FROM read_parquet('{log_trx_final_path}', union_by_name=true)
        WHERE CAST("DateTime" AS DATE) = CAST('{fecha}' AS DATE)
        """
        
        stats_result = conn.execute(stats_query).fetchone()
        if stats_result:
            results['post_procesamiento']['estadisticas'] = {
                'total_transacciones': stats_result[0],
                'usuarios_unicos_payer': stats_result[1],
                'usuarios_unicos_payee': stats_result[2],
                'monto_total': float(stats_result[3]) if stats_result[3] else 0,
                'tipos_transaccion': stats_result[4],
                'primera_transaccion': stats_result[5].isoformat() if stats_result[5] else None,
                'ultima_transaccion': stats_result[6].isoformat() if stats_result[6] else None
            }
        
        # PASO 4: Validaciones de calidad de datos
        logger.info("Ejecutando validaciones de calidad...")
        
        validations = {}
        
        # Validación 1: Transacciones con montos negativos
        negative_amount_query = f"""
        SELECT COUNT(*) FROM read_parquet('{log_trx_final_path}', union_by_name=true)
        WHERE "Amount" < 0 AND CAST("DateTime" AS DATE) = CAST('{fecha}' AS DATE)
        """
        negative_count = conn.execute(negative_amount_query).fetchone()[0]
        validations['montos_negativos'] = negative_count
        
        # Validación 2: Transacciones sin FromID o ToID
        missing_ids_query = f"""
        SELECT COUNT(*) FROM read_parquet('{log_trx_final_path}', union_by_name=true)
        WHERE ("FromID_Mobiquity" IS NULL OR "ToID_Mobiquity" IS NULL)
        AND CAST("DateTime" AS DATE) = CAST('{fecha}' AS DATE)
        """
        missing_ids_count = conn.execute(missing_ids_query).fetchone()[0]
        validations['ids_faltantes'] = missing_ids_count
        
        # Validación 3: Transacciones duplicadas por TransactionID
        duplicate_query = f"""
        SELECT COUNT(*) FROM (
            SELECT "TransactionID", COUNT(*) as cnt
            FROM read_parquet('{log_trx_final_path}', union_by_name=true)
            WHERE CAST("DateTime" AS DATE) = CAST('{fecha}' AS DATE)
            GROUP BY "TransactionID"
            HAVING COUNT(*) > 1
        )
        """
        duplicate_count = conn.execute(duplicate_query).fetchone()[0]
        validations['transacciones_duplicadas'] = duplicate_count
        
        results['post_procesamiento']['validaciones'] = validations
        
        # Log de validaciones
        if negative_count > 0:
            logger.warning(f"Se encontraron {negative_count} transacciones con montos negativos")
        if missing_ids_count > 0:
            logger.warning(f"Se encontraron {missing_ids_count} transacciones sin FromID o ToID")
        if duplicate_count > 0:
            logger.warning(f"Se encontraron {duplicate_count} TransactionIDs duplicados")
        
        logger.info("Post-procesamiento LOG_TRANSACCIONES completado exitosamente")
        return results
        
    except Exception as e:
        error_msg = f"Error en post-procesamiento LOG_TRANSACCIONES: {e}"
        logger.error(error_msg)
        results['errores'].append(error_msg)
        raise

def setup_bank_domain_mappings(config):
    """
    Configura los mapeos de Bank Domain desde la configuración
    
    Args:
        config (ConfigParser): Configuración del reporte
    
    Returns:
        dict: Mapeos de bank domain
    """
    try:
        bank_domain_str = config.get('pipeline_config', 'bank_domain_accounts', fallback='{}')
        bank_domain_accounts = json.loads(bank_domain_str)
        return bank_domain_accounts
    except Exception as e:
        logging.warning(f"Error cargando bank_domain_accounts: {e}")
        return {
            'BNACION': '1334853',
            'CCUSCO': '1464437', 
            'CRANDES': '1414519',
            '0231FCONFIANZA': '1882233',
            '0144QAPAQ': '1131834',
            'FCOMPARTAMOS': '1188057'
        }

def setup_special_users(config):
    """
    Configura la lista de usuarios especiales desde la configuración
    
    Args:
        config (ConfigParser): Configuración del reporte
    
    Returns:
        list: Lista de usuarios especiales
    """
    try:
        special_users_str = config.get('pipeline_config', 'special_users', fallback='[]')
        special_users = json.loads(special_users_str)
        return special_users
    except Exception as e:
        logging.warning(f"Error cargando special_users: {e}")
        return [
            '466787', '580943', '1597312', '1885838',
            'US.****************', 'US.****************', 'US.****************'
        ]
