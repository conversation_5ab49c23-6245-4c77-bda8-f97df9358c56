# REPORTE_LOG_TRANSACCIONES - Framework LOGICA

## 🎯 Descripción

Migración completa del pipeline `pipeline_log_transacciones_duckdb.py` al framework LOGICA genérico. Mantiene **100% de la funcionalidad original** mientras adopta una arquitectura modular y configurable.

## 🏗️ Arquitectura

### **ANTES (Pipeline Monolítico)**
```
pipeline_log_transacciones_duckdb.py
├── Configuración hardcodeada
├── Lógica SQL embebida en Python
├── Procesamiento secuencial fijo
└── Manejo de errores básico
```

### **DESPUÉS (Framework LOGICA)**
```
REPORTE_LOG_TRANSACCIONES/
├── config.ini                    # Configuración externa
├── queries/                      # Queries SQL separados
│   ├── sp_pre_log_trx.sql       # SP_PRE_LOG_TRX
│   ├── sp_pre_log_trx_update.sql # SP_PRE_LOG_TRX_UPDATE  
│   ├── sp_log_trx.sql           # SP_LOG_TRX
│   └── extract_final_csv.sql    # Extracción final
├── post_process.py              # Post-procesamiento
├── run_log_transacciones.py     # Runner personalizado
└── README.md                    # Esta documentación
```

## 🔧 Configuración

### **config.ini**
```ini
[general]
nombre_reporte = REPORTE_LOG_TRANSACCIONES
output_dir = S3_LOG_TRANSACCIONES_OUTPUT

[fecha]
rango = false
dias_atras = 1

[s3_sources]
# Tablas PDP (Silver Zone)
mtx_transaction_header = prd-datalake-silver-zone-************, PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA, us-east-1
mtx_transaction_items = prd-datalake-silver-zone-************, PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA, us-east-1
# ... más fuentes

[queries]
query_list = sp_pre_log_trx, sp_pre_log_trx_update, sp_log_trx, extract_final_csv

[pipeline_config]
# Configuración específica del pipeline
bank_domain_accounts = {"BNACION": "1334853", "CCUSCO": "1464437", ...}
special_users = ["466787", "580943", ...]
```

## 🚀 Ejecución

### **Método 1: Framework LOGICA Genérico**
```bash
cd /home/<USER>/REPORTE_2025/reports/generate/LOGICA
python run_reporte.py REPORTE_LOG_TRANSACCIONES [FECHA]
```

### **Método 2: Runner Personalizado**
```bash
cd /home/<USER>/REPORTE_2025/reports/generate/LOGICA/REPORTES/REPORTE_LOG_TRANSACCIONES
python run_log_transacciones.py [FECHA]
```

### **Ejemplos**
```bash
# Usar fecha por defecto (ayer)
python run_reporte.py REPORTE_LOG_TRANSACCIONES

# Fecha específica
python run_reporte.py REPORTE_LOG_TRANSACCIONES 2025-06-20

# Runner personalizado con fecha específica
python run_log_transacciones.py 2025-06-20
```

## 📊 Flujo de Procesamiento

### **Etapa 1: SP_PRE_LOG_TRX**
- **Archivo**: `queries/sp_pre_log_trx.sql`
- **Función**: Procesa transacciones desde MTX_TRANSACTION_HEADER/ITEMS
- **Salida**: `TEMP_LOGS_TRANSACCIONES/{YYYYMMDD}/PRE_LOG_TRX.parquet`
- **Lógica**: 
  - Filtros Oracle exactos para homologación 100%
  - Mapeo de TRX_SERVICE, CONTEXT, COMMENT
  - Manejo de usuarios especiales (US.xxxxx, 945661)
  - Lógica de Bank Domain

### **Etapa 2: SP_PRE_LOG_TRX_UPDATE**
- **Archivo**: `queries/sp_pre_log_trx_update.sql`
- **Función**: Aplica actualizaciones específicas de Niubiz
- **Salida**: Modifica `PRE_LOG_TRX.parquet` in-place
- **Lógica**:
  - Transformación Context para REFUND
  - Actualización Comment específica

### **Etapa 3: SP_LOG_TRX**
- **Archivo**: `queries/sp_log_trx.sql`
- **Función**: Procesa PRE_LOG_TRX y aplica mapeos finales
- **Salida**: `TEMP_LOGS_TRANSACCIONES/{YYYYMMDD}/LOG_TRX_FINAL.parquet`
- **Lógica**:
  - Mapeo con USER_ACCOUNT_HISTORY
  - Transformaciones finales de AccountID

### **Etapa 4: EXTRACT_FINAL_CSV**
- **Archivo**: `queries/extract_final_csv.sql`
- **Función**: Extrae CSV final para reportes
- **Salida**: `output/TR-{YYYYMMDD}.csv`

### **Etapa 5: Post-Procesamiento**
- **Archivo**: `post_process.py`
- **Función**: Validaciones y estadísticas
- **Salida**: Logs de validación y métricas

## 🔍 Validaciones

### **Validaciones de Calidad**
- ✅ Transacciones con montos negativos
- ✅ Transacciones sin FromID o ToID
- ✅ TransactionIDs duplicados
- ✅ Estadísticas de resumen

### **Homologación 100%**
- ✅ Filtros Oracle exactos
- ✅ Lógica de negocio idéntica
- ✅ Mapeos de usuarios especiales
- ✅ Transformaciones Context/Comment

## 🧪 Pruebas

### **Ejecutar Pruebas**
```bash
cd /home/<USER>/REPORTE_2025/reports/generate/LOGICA
python test_log_transacciones.py
```

### **Pruebas Incluidas**
- ✅ Verificación de configuración
- ✅ Existencia de queries
- ✅ Scripts de post-procesamiento
- ✅ Imports de módulos
- ✅ Sintaxis de queries
- ✅ Prueba en seco del runner

## 📁 Archivos Generados

### **Archivos Temporales**
```
TEMP_LOGS_TRANSACCIONES/
└── {YYYYMMDD}/
    ├── PRE_LOG_TRX.parquet
    └── LOG_TRX_FINAL.parquet
```

### **Archivos de Salida**
```
output/
└── TR-{YYYYMMDD}.csv
```

### **Logs**
```
logs/
└── log_transacciones_runner_{timestamp}.log
```

## 🔧 Mantenimiento

### **Agregar Nueva Fuente S3**
1. Editar `config.ini` sección `[s3_sources]`
2. Usar en queries como `{nombre_fuente}_path`

### **Modificar Lógica de Negocio**
1. Editar el query SQL correspondiente en `queries/`
2. Mantener placeholders para variables dinámicas

### **Agregar Validación**
1. Editar `post_process.py`
2. Agregar nueva validación en función `post_process_log_transacciones`

## 🚨 Troubleshooting

### **Error: Archivo de configuración no encontrado**
```bash
# Verificar ruta
ls -la CONFIGURACIONES/REPORTE_LOG_TRANSACCIONES/config.ini
```

### **Error: Query file not found**
```bash
# Verificar queries
ls -la REPORTES/REPORTE_LOG_TRANSACCIONES/queries/
```

### **Error: Import de runner personalizado**
```bash
# Verificar Python path
cd LOGICA
python -c "from REPORTES.REPORTE_LOG_TRANSACCIONES.run_log_transacciones import LogTransaccionesRunner"
```

### **Error: Credenciales S3**
```bash
# Verificar AWS CLI
aws sts get-caller-identity
```

## 📈 Ventajas de la Migración

### **✅ Mantenibilidad**
- Configuración externa vs. hardcodeada
- Queries SQL separados vs. embebidos
- Lógica modular vs. monolítica

### **✅ Reutilización**
- Framework genérico para otros reportes
- Componentes intercambiables
- Configuración por archivo

### **✅ Escalabilidad**
- Fácil agregar nuevas fuentes
- Post-procesamiento personalizable
- Validaciones extensibles

### **✅ Robustez**
- Manejo de errores mejorado
- Logging detallado
- Validaciones automáticas

## 🎯 Compatibilidad

### **100% Compatible con Pipeline Original**
- ✅ Mismos archivos de entrada (S3)
- ✅ Mismos archivos de salida (CSV)
- ✅ Misma lógica de negocio
- ✅ Mismos resultados (homologación perfecta)

### **Mejoras Adicionales**
- ✅ Configuración flexible
- ✅ Logging mejorado
- ✅ Validaciones automáticas
- ✅ Arquitectura modular

---

## 📞 Soporte

Para dudas o problemas con la migración, revisar:
1. Este README.md
2. Logs en `logs/`
3. Ejecutar `python test_log_transacciones.py`
4. Comparar con pipeline original en `S3_LOG_TRANSACCIONES/`
