[general]
nombre_reporte = REPORTE_LOG_TRANSACCIONES
output_dir = S3_LOG_TRANSACCIONES_OUTPUT
log_dir = logs

[fecha]
# Si rango=true, usar rango_inicio y rango_fin
# Si rango=false, usar dias_atras
rango = false
dias_atras = 1
rango_inicio = 2025-06-15
rango_fin = 2025-06-15

[s3_sources]
# Formato: bucket, prefix, region
# Cada fuente genera automáticamente las siguientes variables en el SQL:
# - {nombre_fuente}_bucket: El bucket de S3
# - {nombre_fuente}_prefix: El prefijo dentro del bucket
# - {nombre_fuente}_region: La región de AWS
# - {nombre_fuente}_path: La ruta completa

# === TABLAS PDP (SILVER ZONE) ===
# Tablas particionadas por fecha (YYYY/MM/DD)
mtx_transaction_header = prd-datalake-silver-zone-************, PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_HEADER_ORA, us-east-1
mtx_transaction_items = prd-datalake-silver-zone-************, PDP_PROD10_MAINDBBUS/MTX_TRANSACTION_ITEMS_ORA, us-east-1

# Tablas estáticas (consolidado_puro.parquet)
mtx_wallet = prd-datalake-silver-zone-************, PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA, us-east-1
user_profile = prd-datalake-silver-zone-************, PDP_PROD10_MAINDB/USER_PROFILE_ORA, us-east-1
sys_service_types = prd-datalake-silver-zone-************, PDP_PROD10_MAINDB/SYS_SERVICE_TYPES_ORA, us-east-1
channel_grades = prd-datalake-silver-zone-************, PDP_PROD10_MAINDB/CHANNEL_GRADES_ORA, us-east-1
marketing_profile = prd-datalake-silver-zone-************, PDP_PROD10_MAINDB/MARKETING_PROFILE_ORA, us-east-1
mtx_categories = prd-datalake-silver-zone-************, PDP_PROD10_MAINDB/MTX_CATEGORIES_ORA, us-east-1
issuer_details = prd-datalake-silver-zone-************, PDP_PROD10_MAINDB/ISSUER_DETAILS_ORA, us-east-1
sys_service_provider = prd-datalake-silver-zone-************, PDP_PROD10_MAINDB/SYS_SERVICE_PROVIDER_ORA, us-east-1
user_accounts = prd-datalake-silver-zone-************, PDP_PROD10_MAINDB/USER_ACCOUNTS_ORA, us-east-1

# === TABLAS DATALAKE (GOLDEN ZONE) ===
user_data_trx = prd-datalake-golden-zone-************, LOGS_USUARIOS, us-east-1
user_account_history = prd-datalake-golden-zone-************, LOGS_USUARIOS, us-east-1

# === TABLAS RAW PARA LÓGICA TEMPORAL ===
mtx_wallet_raw = prd-datalake-silver-zone-************, PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA, us-east-1

[queries]
# Lista de queries a ejecutar en orden
query_list = sp_pre_log_trx, sp_pre_log_trx_update, sp_log_trx, extract_final_csv

[pipeline_config]
# Configuración específica del pipeline LOG_TRANSACCIONES
# Mapeo de Bank Domains para cuentas especiales (igual que Oracle)
bank_domain_accounts = {"BNACION": "1334853", "CCUSCO": "1464437", "CRANDES": "1414519", "0231FCONFIANZA": "1882233", "0144QAPAQ": "1131834", "FCOMPARTAMOS": "1188057"}

# Usuarios especiales para lógica de BILLPAY (igual que Oracle líneas 138-139)
special_users = ["466787", "580943", "1597312", "1885838", "US.****************", "US.****************", "US.****************"]

# Directorios de trabajo
temp_dir = TEMP_LOGS_TRANSACCIONES
output_dir_csv = output

[post_processing]
# Scripts de post-procesamiento
scripts = REPORTES/REPORTE_LOG_TRANSACCIONES/post_process.py
