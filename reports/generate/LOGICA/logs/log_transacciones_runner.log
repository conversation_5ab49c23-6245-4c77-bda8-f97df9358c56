2025-06-20 19:30:00,229 - Log<PERSON><PERSON><PERSON><PERSON>onesRunner - INFO - Configurando credenciales S3...
2025-06-20 19:30:00,269 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-20 19:30:00,634 - Log<PERSON><PERSON>saccionesRunner - INFO - Credenciales S3 configuradas exitosamente
2025-06-20 19:30:11,279 - Log<PERSON><PERSON><PERSON><PERSON>onesRunner - INFO - Configurando credenciales S3...
2025-06-20 19:30:11,298 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-20 19:30:11,336 - LogTransaccionesRunner - INFO - Credenciales S3 configuradas exitosamente
2025-06-20 19:30:11,336 - LogTransaccionesRunner - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-19
2025-06-20 19:30:11,336 - <PERSON>g<PERSON><PERSON><PERSON><PERSON><PERSON>Runner - INFO - Ejecutando query: sp_pre_log_trx
2025-06-20 19:30:11,337 - LogTransaccionesRunner - ERROR - Error ejecutando query sp_pre_log_trx: Parser Error: syntax error at or near "466787"
2025-06-20 19:30:11,337 - LogTransaccionesRunner - ERROR - Error en etapa sp_pre_log_trx: Parser Error: syntax error at or near "466787"
2025-06-20 19:30:11,337 - LogTransaccionesRunner - ERROR - ❌ Error crítico en pipeline: Parser Error: syntax error at or near "466787"
2025-06-20 19:30:42,971 - LogTransaccionesRunner - INFO - Configurando credenciales S3...
2025-06-20 19:30:42,990 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-20 19:30:43,028 - LogTransaccionesRunner - INFO - Credenciales S3 configuradas exitosamente
2025-06-20 19:30:43,028 - LogTransaccionesRunner - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-19
2025-06-20 19:30:43,028 - LogTransaccionesRunner - INFO - Ejecutando query: sp_pre_log_trx
2025-06-20 19:30:50,722 - LogTransaccionesRunner - INFO - SP_PRE_LOG_TRX completado: 218450 registros -> TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-20 19:30:50,722 - LogTransaccionesRunner - INFO - Etapa sp_pre_log_trx completada en 7.69s
2025-06-20 19:30:50,722 - LogTransaccionesRunner - INFO - Ejecutando query: sp_pre_log_trx_update
2025-06-20 19:30:51,275 - LogTransaccionesRunner - INFO - Query sp_pre_log_trx_update ejecutado (actualización)
2025-06-20 19:30:51,275 - LogTransaccionesRunner - INFO - Etapa sp_pre_log_trx_update completada en 0.55s
2025-06-20 19:30:51,276 - LogTransaccionesRunner - INFO - Ejecutando query: sp_log_trx
2025-06-20 19:30:51,837 - LogTransaccionesRunner - INFO - SP_LOG_TRX completado: 218450 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-20 19:30:51,837 - LogTransaccionesRunner - INFO - Etapa sp_log_trx completada en 0.56s
2025-06-20 19:30:51,837 - LogTransaccionesRunner - INFO - Ejecutando query: extract_final_csv
2025-06-20 19:30:51,837 - LogTransaccionesRunner - ERROR - Error ejecutando query extract_final_csv: 'YYYYMMDD'
2025-06-20 19:30:51,837 - LogTransaccionesRunner - ERROR - Error en etapa extract_final_csv: 'YYYYMMDD'
2025-06-20 19:30:51,837 - LogTransaccionesRunner - ERROR - ❌ Error crítico en pipeline: 'YYYYMMDD'
2025-06-20 19:31:48,369 - LogTransaccionesRunner - INFO - Configurando credenciales S3...
2025-06-20 19:31:48,388 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-20 19:31:48,430 - LogTransaccionesRunner - INFO - Credenciales S3 configuradas exitosamente
2025-06-20 19:31:48,430 - LogTransaccionesRunner - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-19
2025-06-20 19:31:48,430 - LogTransaccionesRunner - INFO - Ejecutando query: sp_pre_log_trx
2025-06-20 19:31:55,186 - LogTransaccionesRunner - INFO - SP_PRE_LOG_TRX completado: 218450 registros -> TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-20 19:31:55,186 - LogTransaccionesRunner - INFO - Etapa sp_pre_log_trx completada en 6.76s
2025-06-20 19:31:55,186 - LogTransaccionesRunner - INFO - Ejecutando query: sp_pre_log_trx_update
2025-06-20 19:31:55,755 - LogTransaccionesRunner - INFO - Query sp_pre_log_trx_update ejecutado (actualización)
2025-06-20 19:31:55,755 - LogTransaccionesRunner - INFO - Etapa sp_pre_log_trx_update completada en 0.57s
2025-06-20 19:31:55,756 - LogTransaccionesRunner - INFO - Ejecutando query: sp_log_trx
2025-06-20 19:31:56,308 - LogTransaccionesRunner - INFO - SP_LOG_TRX completado: 218450 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-20 19:31:56,308 - LogTransaccionesRunner - INFO - Etapa sp_log_trx completada en 0.55s
2025-06-20 19:31:56,308 - LogTransaccionesRunner - INFO - Ejecutando query: extract_final_csv
2025-06-20 19:31:56,308 - LogTransaccionesRunner - ERROR - Error ejecutando query extract_final_csv: 'YYYYMMDD'
2025-06-20 19:31:56,308 - LogTransaccionesRunner - ERROR - Error en etapa extract_final_csv: 'YYYYMMDD'
2025-06-20 19:31:56,308 - LogTransaccionesRunner - ERROR - ❌ Error crítico en pipeline: 'YYYYMMDD'
2025-06-20 19:32:31,156 - LogTransaccionesRunner - INFO - Configurando credenciales S3...
2025-06-20 19:32:31,175 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-20 19:32:31,210 - LogTransaccionesRunner - INFO - Credenciales S3 configuradas exitosamente
2025-06-20 19:32:31,210 - LogTransaccionesRunner - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-19
2025-06-20 19:32:31,210 - LogTransaccionesRunner - INFO - Ejecutando query: sp_pre_log_trx
2025-06-20 19:32:38,394 - LogTransaccionesRunner - INFO - SP_PRE_LOG_TRX completado: 218450 registros -> TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-20 19:32:38,394 - LogTransaccionesRunner - INFO - Etapa sp_pre_log_trx completada en 7.18s
2025-06-20 19:32:38,394 - LogTransaccionesRunner - INFO - Ejecutando query: sp_pre_log_trx_update
2025-06-20 19:32:39,003 - LogTransaccionesRunner - INFO - Query sp_pre_log_trx_update ejecutado (actualización)
2025-06-20 19:32:39,003 - LogTransaccionesRunner - INFO - Etapa sp_pre_log_trx_update completada en 0.61s
2025-06-20 19:32:39,003 - LogTransaccionesRunner - INFO - Ejecutando query: sp_log_trx
2025-06-20 19:32:39,480 - LogTransaccionesRunner - INFO - SP_LOG_TRX completado: 218450 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-20 19:32:39,480 - LogTransaccionesRunner - INFO - Etapa sp_log_trx completada en 0.48s
2025-06-20 19:32:39,480 - LogTransaccionesRunner - INFO - Ejecutando query: extract_final_csv
2025-06-20 19:32:39,480 - LogTransaccionesRunner - INFO - Variables para extract_final_csv: temp_dir=TEMP_LOGS_TRANSACCIONES, date_folder=********, output_dir_csv=output
2025-06-20 19:32:39,480 - LogTransaccionesRunner - ERROR - Error ejecutando query extract_final_csv: 'YYYYMMDD'
2025-06-20 19:32:39,480 - LogTransaccionesRunner - ERROR - Error en etapa extract_final_csv: 'YYYYMMDD'
2025-06-20 19:32:39,480 - LogTransaccionesRunner - ERROR - ❌ Error crítico en pipeline: 'YYYYMMDD'
2025-06-20 19:33:12,802 - LogTransaccionesRunner - INFO - Configurando credenciales S3...
2025-06-20 19:33:12,822 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-20 19:33:12,863 - LogTransaccionesRunner - INFO - Credenciales S3 configuradas exitosamente
2025-06-20 19:33:12,864 - LogTransaccionesRunner - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-19
2025-06-20 19:33:12,864 - LogTransaccionesRunner - INFO - Ejecutando query: sp_pre_log_trx
2025-06-20 19:33:19,056 - LogTransaccionesRunner - INFO - SP_PRE_LOG_TRX completado: 218450 registros -> TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-20 19:33:19,056 - LogTransaccionesRunner - INFO - Etapa sp_pre_log_trx completada en 6.19s
2025-06-20 19:33:19,056 - LogTransaccionesRunner - INFO - Ejecutando query: sp_pre_log_trx_update
2025-06-20 19:33:19,598 - LogTransaccionesRunner - INFO - Query sp_pre_log_trx_update ejecutado (actualización)
2025-06-20 19:33:19,598 - LogTransaccionesRunner - INFO - Etapa sp_pre_log_trx_update completada en 0.54s
2025-06-20 19:33:19,598 - LogTransaccionesRunner - INFO - Ejecutando query: sp_log_trx
2025-06-20 19:33:20,121 - LogTransaccionesRunner - INFO - SP_LOG_TRX completado: 218450 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-20 19:33:20,121 - LogTransaccionesRunner - INFO - Etapa sp_log_trx completada en 0.52s
2025-06-20 19:33:20,121 - LogTransaccionesRunner - INFO - Ejecutando query: extract_final_csv
2025-06-20 19:33:20,121 - LogTransaccionesRunner - INFO - Variables para extract_final_csv: temp_dir=TEMP_LOGS_TRANSACCIONES, date_folder=********, output_dir_csv=output
2025-06-20 19:33:20,121 - LogTransaccionesRunner - INFO - Query original: -- ========================================================================
-- EXTRACT_FINAL_CSV - Migrado del pipeline_log_transacciones_duckdb.py
-- Extrae el CSV final TR-{YYYYMMDD}.csv
-- Replica exactamente la query LOG-TRANSACCIONES.sql
-- ========================================================================
-- CODE NINJA MASTER: Query exacta como LOG-TRANSACCIONES.sql

SELECT *
FROM read_parquet('{temp_dir}/{date_folder}/LOG_TRX_FINAL.parquet', union_by_name=true)
WHERE CAST("DateTime" AS DATE) = CAST('{fecha_inicio}' AS DATE)
ORDER BY "DateTime", "TransferID"

2025-06-20 19:33:20,121 - LogTransaccionesRunner - ERROR - Error de formateo - variable faltante: 'YYYYMMDD'
2025-06-20 19:33:20,121 - LogTransaccionesRunner - ERROR - Error ejecutando query extract_final_csv: 'YYYYMMDD'
2025-06-20 19:33:20,121 - LogTransaccionesRunner - ERROR - Error en etapa extract_final_csv: 'YYYYMMDD'
2025-06-20 19:33:20,121 - LogTransaccionesRunner - ERROR - ❌ Error crítico en pipeline: 'YYYYMMDD'
2025-06-20 19:33:43,555 - LogTransaccionesRunner - INFO - Configurando credenciales S3...
2025-06-20 19:33:43,574 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-20 19:33:43,611 - LogTransaccionesRunner - INFO - Credenciales S3 configuradas exitosamente
2025-06-20 19:33:43,612 - LogTransaccionesRunner - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-19
2025-06-20 19:33:43,612 - LogTransaccionesRunner - INFO - Ejecutando query: sp_pre_log_trx
2025-06-20 19:33:49,853 - LogTransaccionesRunner - INFO - SP_PRE_LOG_TRX completado: 218450 registros -> TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-20 19:33:49,853 - LogTransaccionesRunner - INFO - Etapa sp_pre_log_trx completada en 6.24s
2025-06-20 19:33:49,853 - LogTransaccionesRunner - INFO - Ejecutando query: sp_pre_log_trx_update
2025-06-20 19:33:50,431 - LogTransaccionesRunner - INFO - Query sp_pre_log_trx_update ejecutado (actualización)
2025-06-20 19:33:50,431 - LogTransaccionesRunner - INFO - Etapa sp_pre_log_trx_update completada en 0.58s
2025-06-20 19:33:50,431 - LogTransaccionesRunner - INFO - Ejecutando query: sp_log_trx
2025-06-20 19:33:50,913 - LogTransaccionesRunner - INFO - SP_LOG_TRX completado: 218450 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-20 19:33:50,913 - LogTransaccionesRunner - INFO - Etapa sp_log_trx completada en 0.48s
2025-06-20 19:33:50,913 - LogTransaccionesRunner - INFO - Ejecutando query: extract_final_csv
2025-06-20 19:33:50,913 - LogTransaccionesRunner - INFO - Variables para extract_final_csv: temp_dir=TEMP_LOGS_TRANSACCIONES, date_folder=********, output_dir_csv=output
2025-06-20 19:33:50,913 - LogTransaccionesRunner - INFO - Query original: -- ========================================================================
-- EXTRACT_FINAL_CSV - Migrado del pipeline_log_transacciones_duckdb.py
-- Extrae el CSV final TR-YYYYMMDD.csv
-- Replica exactamente la query LOG-TRANSACCIONES.sql
-- ========================================================================
-- CODE NINJA MASTER: Query exacta como LOG-TRANSACCIONES.sql

SELECT *
FROM read_parquet('{temp_dir}/{date_folder}/LOG_TRX_FINAL.parquet', union_by_name=true)
WHERE CAST("DateTime" AS DATE) = CAST('{fecha_inicio}' AS DATE)
ORDER BY "DateTime", "TransferID"

2025-06-20 19:33:50,913 - LogTransaccionesRunner - INFO - Query formateado: -- ========================================================================
-- EXTRACT_FINAL_CSV - Migrado del pipeline_log_transacciones_duckdb.py
-- Extrae el CSV final TR-YYYYMMDD.csv
-- Replica exactamente la query LOG-TRANSACCIONES.sql
-- ========================================================================
-- CODE NINJA MASTER: Query exacta como LOG-TRANSACCIONES.sql

SELECT *
FROM read_parquet('TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet', union_by_name=true)
WHERE CAST("DateTime" AS DATE) = CAST('2025-06-19' AS DATE)
ORDER BY "DateTime", "TransferID"

2025-06-20 19:33:51,370 - LogTransaccionesRunner - INFO - Extract final CSV completado: 218450 registros -> output/TR-********.csv
2025-06-20 19:33:51,370 - LogTransaccionesRunner - INFO - Etapa extract_final_csv completada en 0.46s
2025-06-20 19:33:51,370 - LogTransaccionesRunner - INFO - Iniciando post-procesamiento...
2025-06-20 19:33:51,370 - LogTransaccionesRunner - INFO - Iniciando post-procesamiento LOG_TRANSACCIONES para fecha: 2025-06-19
2025-06-20 19:33:51,370 - LogTransaccionesRunner - INFO - Generando CSV final...
2025-06-20 19:33:51,827 - LogTransaccionesRunner - INFO - CSV final generado: output/TR-********.csv (218450 registros)
2025-06-20 19:33:51,828 - LogTransaccionesRunner - INFO - Generando estadísticas de resumen...
2025-06-20 19:33:51,866 - LogTransaccionesRunner - ERROR - Error en post-procesamiento LOG_TRANSACCIONES: 'str' object has no attribute 'isoformat'
2025-06-20 19:33:51,866 - LogTransaccionesRunner - ERROR - Error en post-procesamiento: 'str' object has no attribute 'isoformat'
2025-06-20 19:33:51,866 - LogTransaccionesRunner - INFO - ✅ Pipeline LOG_TRANSACCIONES completado en 8.25s
2025-06-20 19:33:51,866 - LogTransaccionesRunner - INFO - 📁 Archivos generados: 2
2025-06-20 19:33:51,866 - LogTransaccionesRunner - WARNING - ⚠️  Se encontraron 1 errores
2025-06-20 19:37:10,347 - LogTransaccionesRunner - INFO - Configurando credenciales S3...
2025-06-20 19:37:10,368 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-20 19:37:10,407 - LogTransaccionesRunner - INFO - Credenciales S3 configuradas exitosamente
2025-06-20 19:37:10,407 - LogTransaccionesRunner - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-19
2025-06-20 19:37:10,407 - LogTransaccionesRunner - INFO - Ejecutando query: sp_pre_log_trx
2025-06-20 19:37:17,172 - LogTransaccionesRunner - INFO - SP_PRE_LOG_TRX completado: 218450 registros -> TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-20 19:37:17,172 - LogTransaccionesRunner - INFO - Etapa sp_pre_log_trx completada en 6.76s
2025-06-20 19:37:17,172 - LogTransaccionesRunner - INFO - Ejecutando query: sp_pre_log_trx_update
2025-06-20 19:37:17,726 - LogTransaccionesRunner - INFO - Query sp_pre_log_trx_update ejecutado (actualización)
2025-06-20 19:37:17,726 - LogTransaccionesRunner - INFO - Etapa sp_pre_log_trx_update completada en 0.55s
2025-06-20 19:37:17,726 - LogTransaccionesRunner - INFO - Ejecutando query: sp_log_trx
2025-06-20 19:37:17,913 - LogTransaccionesRunner - ERROR - Error ejecutando query sp_log_trx: Binder Error: Table "MTH" does not have a column named "FromAccountID"

Candidate bindings: : "From_AccountID", "To_AccountID"

LINE 24: ...     WHEN H_PAYER.USER_ID IS NOT NULL AND H_PAYER.ACCOUNT_ID = MTH."FromAccountID"
                                                                           ^
2025-06-20 19:37:17,913 - LogTransaccionesRunner - ERROR - Error en etapa sp_log_trx: Binder Error: Table "MTH" does not have a column named "FromAccountID"

Candidate bindings: : "From_AccountID", "To_AccountID"

LINE 24: ...     WHEN H_PAYER.USER_ID IS NOT NULL AND H_PAYER.ACCOUNT_ID = MTH."FromAccountID"
                                                                           ^
2025-06-20 19:37:17,913 - LogTransaccionesRunner - ERROR - ❌ Error crítico en pipeline: Binder Error: Table "MTH" does not have a column named "FromAccountID"

Candidate bindings: : "From_AccountID", "To_AccountID"

LINE 24: ...     WHEN H_PAYER.USER_ID IS NOT NULL AND H_PAYER.ACCOUNT_ID = MTH."FromAccountID"
                                                                           ^
2025-06-20 19:38:05,964 - LogTransaccionesRunner - INFO - Configurando credenciales S3...
2025-06-20 19:38:05,983 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-20 19:38:06,018 - LogTransaccionesRunner - INFO - Credenciales S3 configuradas exitosamente
2025-06-20 19:38:06,018 - LogTransaccionesRunner - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-19
2025-06-20 19:38:06,018 - LogTransaccionesRunner - INFO - Ejecutando query: sp_pre_log_trx
2025-06-20 19:38:12,366 - LogTransaccionesRunner - INFO - SP_PRE_LOG_TRX completado: 218450 registros -> TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-20 19:38:12,366 - LogTransaccionesRunner - INFO - Etapa sp_pre_log_trx completada en 6.35s
2025-06-20 19:38:12,366 - LogTransaccionesRunner - INFO - Ejecutando query: sp_pre_log_trx_update
2025-06-20 19:38:12,960 - LogTransaccionesRunner - INFO - Query sp_pre_log_trx_update ejecutado (actualización)
2025-06-20 19:38:12,960 - LogTransaccionesRunner - INFO - Etapa sp_pre_log_trx_update completada en 0.59s
2025-06-20 19:38:12,960 - LogTransaccionesRunner - INFO - Ejecutando query: sp_log_trx
2025-06-20 19:38:13,148 - LogTransaccionesRunner - ERROR - Error ejecutando query sp_log_trx: Binder Error: Table "MTH" does not have a column named "FromAccountID"

Candidate bindings: : "From_AccountID", "To_AccountID"

LINE 24: ...     WHEN H_PAYER.USER_ID IS NOT NULL AND H_PAYER.ACCOUNT_ID = MTH."FromAccountID"
                                                                           ^
2025-06-20 19:38:13,148 - LogTransaccionesRunner - ERROR - Error en etapa sp_log_trx: Binder Error: Table "MTH" does not have a column named "FromAccountID"

Candidate bindings: : "From_AccountID", "To_AccountID"

LINE 24: ...     WHEN H_PAYER.USER_ID IS NOT NULL AND H_PAYER.ACCOUNT_ID = MTH."FromAccountID"
                                                                           ^
2025-06-20 19:38:13,148 - LogTransaccionesRunner - ERROR - ❌ Error crítico en pipeline: Binder Error: Table "MTH" does not have a column named "FromAccountID"

Candidate bindings: : "From_AccountID", "To_AccountID"

LINE 24: ...     WHEN H_PAYER.USER_ID IS NOT NULL AND H_PAYER.ACCOUNT_ID = MTH."FromAccountID"
                                                                           ^
2025-06-20 19:39:07,239 - LogTransaccionesRunner - INFO - Configurando credenciales S3...
2025-06-20 19:39:07,258 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-20 19:39:07,297 - LogTransaccionesRunner - INFO - Credenciales S3 configuradas exitosamente
2025-06-20 19:39:07,297 - LogTransaccionesRunner - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-19
2025-06-20 19:39:07,297 - LogTransaccionesRunner - INFO - Ejecutando query: sp_pre_log_trx
2025-06-20 19:39:13,873 - LogTransaccionesRunner - INFO - SP_PRE_LOG_TRX completado: 218450 registros -> TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-20 19:39:13,873 - LogTransaccionesRunner - INFO - Etapa sp_pre_log_trx completada en 6.58s
2025-06-20 19:39:13,873 - LogTransaccionesRunner - INFO - Ejecutando query: sp_pre_log_trx_update
2025-06-20 19:39:14,458 - LogTransaccionesRunner - INFO - Query sp_pre_log_trx_update ejecutado (actualización)
2025-06-20 19:39:14,458 - LogTransaccionesRunner - INFO - Etapa sp_pre_log_trx_update completada en 0.59s
2025-06-20 19:39:14,458 - LogTransaccionesRunner - INFO - Ejecutando query: sp_log_trx
2025-06-20 19:39:15,069 - LogTransaccionesRunner - INFO - SP_LOG_TRX completado: 218450 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-20 19:39:15,069 - LogTransaccionesRunner - INFO - Etapa sp_log_trx completada en 0.61s
2025-06-20 19:39:15,069 - LogTransaccionesRunner - INFO - Ejecutando query: extract_final_csv
2025-06-20 19:39:15,069 - LogTransaccionesRunner - INFO - Variables para extract_final_csv: temp_dir=TEMP_LOGS_TRANSACCIONES, date_folder=********, output_dir_csv=output
2025-06-20 19:39:15,069 - LogTransaccionesRunner - INFO - Query original: -- ========================================================================
-- EXTRACT_FINAL_CSV - Migrado del pipeline_log_transacciones_duckdb.py
-- Extrae el CSV final TR-YYYYMMDD.csv
-- Replica exactamente la query LOG-TRANSACCIONES.sql
-- ========================================================================
-- CODE NINJA MASTER: Query exacta como LOG-TRANSACCIONES.sql

SELECT *
FROM read_parquet('{temp_dir}/{date_folder}/LOG_TRX_FINAL.parquet', union_by_name=true)
WHERE CAST("DateTime" AS DATE) = CAST('{fecha_inicio}' AS DATE)
ORDER BY "DateTime", "TransferID"

2025-06-20 19:39:15,069 - LogTransaccionesRunner - INFO - Query formateado: -- ========================================================================
-- EXTRACT_FINAL_CSV - Migrado del pipeline_log_transacciones_duckdb.py
-- Extrae el CSV final TR-YYYYMMDD.csv
-- Replica exactamente la query LOG-TRANSACCIONES.sql
-- ========================================================================
-- CODE NINJA MASTER: Query exacta como LOG-TRANSACCIONES.sql

SELECT *
FROM read_parquet('TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet', union_by_name=true)
WHERE CAST("DateTime" AS DATE) = CAST('2025-06-19' AS DATE)
ORDER BY "DateTime", "TransferID"

2025-06-20 19:39:15,070 - LogTransaccionesRunner - ERROR - Error ejecutando query extract_final_csv: Binder Error: Referenced column "TransferID" not found in FROM clause!
Candidate bindings: "TransactionID", "RealUser", "ToProfile", "TransactionType", "FromID"

LINE 11: ORDER BY "DateTime", "TransferID"
                              ^
2025-06-20 19:39:15,070 - LogTransaccionesRunner - ERROR - Error en etapa extract_final_csv: Binder Error: Referenced column "TransferID" not found in FROM clause!
Candidate bindings: "TransactionID", "RealUser", "ToProfile", "TransactionType", "FromID"

LINE 11: ORDER BY "DateTime", "TransferID"
                              ^
2025-06-20 19:39:15,070 - LogTransaccionesRunner - ERROR - ❌ Error crítico en pipeline: Binder Error: Referenced column "TransferID" not found in FROM clause!
Candidate bindings: "TransactionID", "RealUser", "ToProfile", "TransactionType", "FromID"

LINE 11: ORDER BY "DateTime", "TransferID"
                              ^
2025-06-20 19:39:41,424 - LogTransaccionesRunner - INFO - Configurando credenciales S3...
2025-06-20 19:39:41,443 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-20 19:39:41,480 - LogTransaccionesRunner - INFO - Credenciales S3 configuradas exitosamente
2025-06-20 19:39:41,481 - LogTransaccionesRunner - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-19
2025-06-20 19:39:41,481 - LogTransaccionesRunner - INFO - Ejecutando query: sp_pre_log_trx
2025-06-20 19:39:47,531 - LogTransaccionesRunner - INFO - SP_PRE_LOG_TRX completado: 218450 registros -> TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-20 19:39:47,531 - LogTransaccionesRunner - INFO - Etapa sp_pre_log_trx completada en 6.05s
2025-06-20 19:39:47,531 - LogTransaccionesRunner - INFO - Ejecutando query: sp_pre_log_trx_update
2025-06-20 19:39:48,100 - LogTransaccionesRunner - INFO - Query sp_pre_log_trx_update ejecutado (actualización)
2025-06-20 19:39:48,100 - LogTransaccionesRunner - INFO - Etapa sp_pre_log_trx_update completada en 0.57s
2025-06-20 19:39:48,101 - LogTransaccionesRunner - INFO - Ejecutando query: sp_log_trx
2025-06-20 19:39:48,660 - LogTransaccionesRunner - INFO - SP_LOG_TRX completado: 218450 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-20 19:39:48,660 - LogTransaccionesRunner - INFO - Etapa sp_log_trx completada en 0.56s
2025-06-20 19:39:48,660 - LogTransaccionesRunner - INFO - Ejecutando query: extract_final_csv
2025-06-20 19:39:48,661 - LogTransaccionesRunner - INFO - Variables para extract_final_csv: temp_dir=TEMP_LOGS_TRANSACCIONES, date_folder=********, output_dir_csv=output
2025-06-20 19:39:48,661 - LogTransaccionesRunner - INFO - Query original: -- ========================================================================
-- EXTRACT_FINAL_CSV - Migrado del pipeline_log_transacciones_duckdb.py
-- Extrae el CSV final TR-YYYYMMDD.csv
-- Replica exactamente la query LOG-TRANSACCIONES.sql
-- ========================================================================
-- CODE NINJA MASTER: Query exacta como LOG-TRANSACCIONES.sql

SELECT *
FROM read_parquet('{temp_dir}/{date_folder}/LOG_TRX_FINAL.parquet', union_by_name=true)
WHERE CAST("DateTime" AS DATE) = CAST('{fecha_inicio}' AS DATE)
ORDER BY "DateTime", "TransactionID"

2025-06-20 19:39:48,661 - LogTransaccionesRunner - INFO - Query formateado: -- ========================================================================
-- EXTRACT_FINAL_CSV - Migrado del pipeline_log_transacciones_duckdb.py
-- Extrae el CSV final TR-YYYYMMDD.csv
-- Replica exactamente la query LOG-TRANSACCIONES.sql
-- ========================================================================
-- CODE NINJA MASTER: Query exacta como LOG-TRANSACCIONES.sql

SELECT *
FROM read_parquet('TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet', union_by_name=true)
WHERE CAST("DateTime" AS DATE) = CAST('2025-06-19' AS DATE)
ORDER BY "DateTime", "TransactionID"

2025-06-20 19:39:49,211 - LogTransaccionesRunner - INFO - Extract final CSV completado: 218450 registros -> output/TR-********.csv
2025-06-20 19:39:49,211 - LogTransaccionesRunner - INFO - Etapa extract_final_csv completada en 0.55s
2025-06-20 19:39:49,211 - LogTransaccionesRunner - INFO - Iniciando post-procesamiento...
2025-06-20 19:39:49,211 - LogTransaccionesRunner - INFO - Iniciando post-procesamiento LOG_TRANSACCIONES para fecha: 2025-06-19
2025-06-20 19:39:49,211 - LogTransaccionesRunner - INFO - Generando CSV final...
2025-06-20 19:39:49,223 - LogTransaccionesRunner - ERROR - Error en post-procesamiento LOG_TRANSACCIONES: Binder Error: Referenced column "TransferID" not found in FROM clause!
Candidate bindings: "TransactionID", "RealUser", "ToProfile", "TransactionType", "FromID"

LINE 6:             ORDER BY "DateTime", "TransferID"
                                         ^
2025-06-20 19:39:49,223 - LogTransaccionesRunner - ERROR - Error en post-procesamiento: Binder Error: Referenced column "TransferID" not found in FROM clause!
Candidate bindings: "TransactionID", "RealUser", "ToProfile", "TransactionType", "FromID"

LINE 6:             ORDER BY "DateTime", "TransferID"
                                         ^
2025-06-20 19:39:49,223 - LogTransaccionesRunner - INFO - ✅ Pipeline LOG_TRANSACCIONES completado en 7.74s
2025-06-20 19:39:49,223 - LogTransaccionesRunner - INFO - 📁 Archivos generados: 2
2025-06-20 19:39:49,223 - LogTransaccionesRunner - WARNING - ⚠️  Se encontraron 1 errores
2025-06-20 19:44:27,386 - LogTransaccionesRunner - INFO - Configurando credenciales S3...
2025-06-20 19:44:27,406 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-20 19:44:27,446 - LogTransaccionesRunner - INFO - Credenciales S3 configuradas exitosamente
2025-06-20 19:44:27,447 - LogTransaccionesRunner - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-19
2025-06-20 19:44:27,447 - LogTransaccionesRunner - INFO - Ejecutando query: sp_pre_log_trx
2025-06-20 19:44:30,330 - LogTransaccionesRunner - ERROR - Error ejecutando query sp_pre_log_trx: Binder Error: Values list "TDD" does not have a column named "SERVICE_TYPE"

LINE 393: WHERE TDD.SERVICE_TYPE NOT IN ('MULTIDRCR','FTBOA','MERCHPAY'...
                ^
2025-06-20 19:44:30,331 - LogTransaccionesRunner - ERROR - Error en etapa sp_pre_log_trx: Binder Error: Values list "TDD" does not have a column named "SERVICE_TYPE"

LINE 393: WHERE TDD.SERVICE_TYPE NOT IN ('MULTIDRCR','FTBOA','MERCHPAY'...
                ^
2025-06-20 19:44:30,331 - LogTransaccionesRunner - ERROR - ❌ Error crítico en pipeline: Binder Error: Values list "TDD" does not have a column named "SERVICE_TYPE"

LINE 393: WHERE TDD.SERVICE_TYPE NOT IN ('MULTIDRCR','FTBOA','MERCHPAY'...
                ^
2025-06-20 19:45:03,249 - LogTransaccionesRunner - INFO - Configurando credenciales S3...
2025-06-20 19:45:03,269 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-20 19:45:03,308 - LogTransaccionesRunner - INFO - Credenciales S3 configuradas exitosamente
2025-06-20 19:45:03,308 - LogTransaccionesRunner - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-19
2025-06-20 19:45:03,308 - LogTransaccionesRunner - INFO - Ejecutando query: sp_pre_log_trx
2025-06-20 19:45:10,166 - LogTransaccionesRunner - INFO - SP_PRE_LOG_TRX completado: 212569 registros -> TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-20 19:45:10,166 - LogTransaccionesRunner - INFO - Etapa sp_pre_log_trx completada en 6.86s
2025-06-20 19:45:10,166 - LogTransaccionesRunner - INFO - Ejecutando query: sp_pre_log_trx_update
2025-06-20 19:45:10,763 - LogTransaccionesRunner - INFO - Query sp_pre_log_trx_update ejecutado (actualización)
2025-06-20 19:45:10,764 - LogTransaccionesRunner - INFO - Etapa sp_pre_log_trx_update completada en 0.60s
2025-06-20 19:45:10,764 - LogTransaccionesRunner - INFO - Ejecutando query: sp_log_trx
2025-06-20 19:45:11,263 - LogTransaccionesRunner - INFO - SP_LOG_TRX completado: 212569 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-20 19:45:11,264 - LogTransaccionesRunner - INFO - Etapa sp_log_trx completada en 0.50s
2025-06-20 19:45:11,264 - LogTransaccionesRunner - INFO - Ejecutando query: extract_final_csv
2025-06-20 19:45:11,264 - LogTransaccionesRunner - INFO - Variables para extract_final_csv: temp_dir=TEMP_LOGS_TRANSACCIONES, date_folder=********, output_dir_csv=output
2025-06-20 19:45:11,264 - LogTransaccionesRunner - INFO - Query original: -- ========================================================================
-- EXTRACT_FINAL_CSV - Migrado del pipeline_log_transacciones_duckdb.py
-- Extrae el CSV final TR-YYYYMMDD.csv
-- Replica exactamente la query LOG-TRANSACCIONES.sql
-- ========================================================================
-- CODE NINJA MASTER: Query exacta como LOG-TRANSACCIONES.sql

SELECT *
FROM read_parquet('{temp_dir}/{date_folder}/LOG_TRX_FINAL.parquet', union_by_name=true)
WHERE CAST("DateTime" AS DATE) = CAST('{fecha_inicio}' AS DATE)
ORDER BY "DateTime", "TransactionID"

2025-06-20 19:45:11,264 - LogTransaccionesRunner - INFO - Query formateado: -- ========================================================================
-- EXTRACT_FINAL_CSV - Migrado del pipeline_log_transacciones_duckdb.py
-- Extrae el CSV final TR-YYYYMMDD.csv
-- Replica exactamente la query LOG-TRANSACCIONES.sql
-- ========================================================================
-- CODE NINJA MASTER: Query exacta como LOG-TRANSACCIONES.sql

SELECT *
FROM read_parquet('TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet', union_by_name=true)
WHERE CAST("DateTime" AS DATE) = CAST('2025-06-19' AS DATE)
ORDER BY "DateTime", "TransactionID"

2025-06-20 19:45:11,807 - LogTransaccionesRunner - INFO - Extract final CSV completado: 212569 registros -> output/TR-********.csv
2025-06-20 19:45:11,807 - LogTransaccionesRunner - INFO - Etapa extract_final_csv completada en 0.54s
2025-06-20 19:45:11,807 - LogTransaccionesRunner - INFO - Iniciando post-procesamiento...
2025-06-20 19:45:11,807 - LogTransaccionesRunner - INFO - Iniciando post-procesamiento LOG_TRANSACCIONES para fecha: 2025-06-19
2025-06-20 19:45:11,807 - LogTransaccionesRunner - INFO - Generando CSV final...
2025-06-20 19:45:11,808 - LogTransaccionesRunner - ERROR - Error en post-procesamiento LOG_TRANSACCIONES: Binder Error: Referenced column "TransferID" not found in FROM clause!
Candidate bindings: "TransactionID", "RealUser", "ToProfile", "TransactionType", "FromID"

LINE 6:             ORDER BY "DateTime", "TransferID"
                                         ^
2025-06-20 19:45:11,808 - LogTransaccionesRunner - ERROR - Error en post-procesamiento: Binder Error: Referenced column "TransferID" not found in FROM clause!
Candidate bindings: "TransactionID", "RealUser", "ToProfile", "TransactionType", "FromID"

LINE 6:             ORDER BY "DateTime", "TransferID"
                                         ^
2025-06-20 19:45:11,808 - LogTransaccionesRunner - INFO - ✅ Pipeline LOG_TRANSACCIONES completado en 8.50s
2025-06-20 19:45:11,809 - LogTransaccionesRunner - INFO - 📁 Archivos generados: 2
2025-06-20 19:45:11,809 - LogTransaccionesRunner - WARNING - ⚠️  Se encontraron 1 errores
2025-06-20 19:46:39,729 - LogTransaccionesRunner - INFO - Configurando credenciales S3...
2025-06-20 19:46:39,749 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-20 19:46:39,787 - LogTransaccionesRunner - INFO - Credenciales S3 configuradas exitosamente
2025-06-20 19:46:39,788 - LogTransaccionesRunner - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-19
2025-06-20 19:46:39,788 - LogTransaccionesRunner - INFO - Ejecutando query: sp_pre_log_trx
2025-06-20 19:46:46,608 - LogTransaccionesRunner - INFO - SP_PRE_LOG_TRX completado: 218442 registros -> TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-20 19:46:46,608 - LogTransaccionesRunner - INFO - Etapa sp_pre_log_trx completada en 6.82s
2025-06-20 19:46:46,608 - LogTransaccionesRunner - INFO - Ejecutando query: sp_pre_log_trx_update
2025-06-20 19:46:47,224 - LogTransaccionesRunner - INFO - Query sp_pre_log_trx_update ejecutado (actualización)
2025-06-20 19:46:47,224 - LogTransaccionesRunner - INFO - Etapa sp_pre_log_trx_update completada en 0.62s
2025-06-20 19:46:47,224 - LogTransaccionesRunner - INFO - Ejecutando query: sp_log_trx
2025-06-20 19:46:47,769 - LogTransaccionesRunner - INFO - SP_LOG_TRX completado: 218442 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-20 19:46:47,769 - LogTransaccionesRunner - INFO - Etapa sp_log_trx completada en 0.55s
2025-06-20 19:46:47,769 - LogTransaccionesRunner - INFO - Ejecutando query: extract_final_csv
2025-06-20 19:46:47,769 - LogTransaccionesRunner - INFO - Variables para extract_final_csv: temp_dir=TEMP_LOGS_TRANSACCIONES, date_folder=********, output_dir_csv=output
2025-06-20 19:46:47,769 - LogTransaccionesRunner - INFO - Query original: -- ========================================================================
-- EXTRACT_FINAL_CSV - Migrado del pipeline_log_transacciones_duckdb.py
-- Extrae el CSV final TR-YYYYMMDD.csv
-- Replica exactamente la query LOG-TRANSACCIONES.sql
-- ========================================================================
-- CODE NINJA MASTER: Query exacta como LOG-TRANSACCIONES.sql

SELECT *
FROM read_parquet('{temp_dir}/{date_folder}/LOG_TRX_FINAL.parquet', union_by_name=true)
WHERE CAST("DateTime" AS DATE) = CAST('{fecha_inicio}' AS DATE)
ORDER BY "DateTime", "TransactionID"

2025-06-20 19:46:47,770 - LogTransaccionesRunner - INFO - Query formateado: -- ========================================================================
-- EXTRACT_FINAL_CSV - Migrado del pipeline_log_transacciones_duckdb.py
-- Extrae el CSV final TR-YYYYMMDD.csv
-- Replica exactamente la query LOG-TRANSACCIONES.sql
-- ========================================================================
-- CODE NINJA MASTER: Query exacta como LOG-TRANSACCIONES.sql

SELECT *
FROM read_parquet('TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet', union_by_name=true)
WHERE CAST("DateTime" AS DATE) = CAST('2025-06-19' AS DATE)
ORDER BY "DateTime", "TransactionID"

2025-06-20 19:46:48,293 - LogTransaccionesRunner - INFO - Extract final CSV completado: 218442 registros -> output/TR-********.csv
2025-06-20 19:46:48,293 - LogTransaccionesRunner - INFO - Etapa extract_final_csv completada en 0.52s
2025-06-20 19:46:48,293 - LogTransaccionesRunner - INFO - Iniciando post-procesamiento...
2025-06-20 19:46:48,293 - LogTransaccionesRunner - INFO - Iniciando post-procesamiento LOG_TRANSACCIONES para fecha: 2025-06-19
2025-06-20 19:46:48,293 - LogTransaccionesRunner - INFO - Generando CSV final...
2025-06-20 19:46:48,295 - LogTransaccionesRunner - ERROR - Error en post-procesamiento LOG_TRANSACCIONES: Binder Error: Referenced column "TransferID" not found in FROM clause!
Candidate bindings: "TransactionID", "RealUser", "ToProfile", "TransactionType", "FromID"

LINE 6:             ORDER BY "DateTime", "TransferID"
                                         ^
2025-06-20 19:46:48,295 - LogTransaccionesRunner - ERROR - Error en post-procesamiento: Binder Error: Referenced column "TransferID" not found in FROM clause!
Candidate bindings: "TransactionID", "RealUser", "ToProfile", "TransactionType", "FromID"

LINE 6:             ORDER BY "DateTime", "TransferID"
                                         ^
2025-06-20 19:46:48,295 - LogTransaccionesRunner - INFO - ✅ Pipeline LOG_TRANSACCIONES completado en 8.51s
2025-06-20 19:46:48,295 - LogTransaccionesRunner - INFO - 📁 Archivos generados: 2
2025-06-20 19:46:48,295 - LogTransaccionesRunner - WARNING - ⚠️  Se encontraron 1 errores
2025-06-20 19:57:30,813 - LogTransaccionesRunner - INFO - Configurando credenciales S3...
2025-06-20 19:57:30,832 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-20 19:57:30,867 - LogTransaccionesRunner - INFO - Credenciales S3 configuradas exitosamente
2025-06-20 19:57:30,868 - LogTransaccionesRunner - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-19
2025-06-20 19:57:30,868 - LogTransaccionesRunner - INFO - Ejecutando query: sp_pre_log_trx
2025-06-20 19:57:30,870 - LogTransaccionesRunner - INFO - SP_PRE_LOG_TRX completado: 1 registros -> TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-20 19:57:30,871 - LogTransaccionesRunner - INFO - Etapa sp_pre_log_trx completada en 0.00s
2025-06-20 19:57:30,871 - LogTransaccionesRunner - INFO - Ejecutando query: sp_pre_log_trx_update
2025-06-20 19:57:30,872 - LogTransaccionesRunner - ERROR - Error ejecutando query sp_pre_log_trx_update: Binder Error: Referenced column "TransactionID" not found in FROM clause!
Candidate bindings: "test_column"

LINE 13:     "TransactionID",
             ^
2025-06-20 19:57:30,872 - LogTransaccionesRunner - ERROR - Error en etapa sp_pre_log_trx_update: Binder Error: Referenced column "TransactionID" not found in FROM clause!
Candidate bindings: "test_column"

LINE 13:     "TransactionID",
             ^
2025-06-20 19:57:30,872 - LogTransaccionesRunner - ERROR - ❌ Error crítico en pipeline: Binder Error: Referenced column "TransactionID" not found in FROM clause!
Candidate bindings: "test_column"

LINE 13:     "TransactionID",
             ^
2025-06-20 19:59:03,173 - LogTransaccionesRunner - INFO - Configurando credenciales S3...
2025-06-20 19:59:03,192 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-20 19:59:03,228 - LogTransaccionesRunner - INFO - Credenciales S3 configuradas exitosamente
2025-06-20 19:59:03,229 - LogTransaccionesRunner - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-19
2025-06-20 19:59:03,229 - LogTransaccionesRunner - INFO - Ejecutando query: sp_pre_log_trx
2025-06-20 19:59:04,887 - LogTransaccionesRunner - INFO - SP_PRE_LOG_TRX completado: 219812 registros -> TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-20 19:59:04,887 - LogTransaccionesRunner - INFO - Etapa sp_pre_log_trx completada en 1.66s
2025-06-20 19:59:04,887 - LogTransaccionesRunner - INFO - Ejecutando query: sp_pre_log_trx_update
2025-06-20 19:59:04,888 - LogTransaccionesRunner - ERROR - Error ejecutando query sp_pre_log_trx_update: Binder Error: Referenced column "FromID_Mobiquity" not found in FROM clause!
Candidate bindings: "FtxnID", "TransactionID"

LINE 19:     "FromID_Mobiquity",
             ^
2025-06-20 19:59:04,888 - LogTransaccionesRunner - ERROR - Error en etapa sp_pre_log_trx_update: Binder Error: Referenced column "FromID_Mobiquity" not found in FROM clause!
Candidate bindings: "FtxnID", "TransactionID"

LINE 19:     "FromID_Mobiquity",
             ^
2025-06-20 19:59:04,889 - LogTransaccionesRunner - ERROR - ❌ Error crítico en pipeline: Binder Error: Referenced column "FromID_Mobiquity" not found in FROM clause!
Candidate bindings: "FtxnID", "TransactionID"

LINE 19:     "FromID_Mobiquity",
             ^
2025-06-20 20:00:52,824 - LogTransaccionesRunner - INFO - Configurando credenciales S3...
2025-06-20 20:00:52,843 - botocore.credentials - INFO - Found credentials from IAM Role: ec-landing-process-01-EC2-Role
2025-06-20 20:00:52,878 - LogTransaccionesRunner - INFO - Credenciales S3 configuradas exitosamente
2025-06-20 20:00:52,878 - LogTransaccionesRunner - INFO - 🚀 Iniciando pipeline LOG_TRANSACCIONES para fecha: 2025-06-19
2025-06-20 20:00:52,879 - LogTransaccionesRunner - INFO - Ejecutando query: sp_pre_log_trx
2025-06-20 20:00:54,374 - LogTransaccionesRunner - INFO - SP_PRE_LOG_TRX completado: 219812 registros -> TEMP_LOGS_TRANSACCIONES/********/PRE_LOG_TRX.parquet
2025-06-20 20:00:54,374 - LogTransaccionesRunner - INFO - Etapa sp_pre_log_trx completada en 1.50s
2025-06-20 20:00:54,375 - LogTransaccionesRunner - INFO - Ejecutando query: sp_pre_log_trx_update
2025-06-20 20:00:54,375 - LogTransaccionesRunner - INFO - Query sp_pre_log_trx_update ejecutado (actualización)
2025-06-20 20:00:54,375 - LogTransaccionesRunner - INFO - Etapa sp_pre_log_trx_update completada en 0.00s
2025-06-20 20:00:54,376 - LogTransaccionesRunner - INFO - Ejecutando query: sp_log_trx
2025-06-20 20:00:54,704 - LogTransaccionesRunner - INFO - SP_LOG_TRX completado: 219812 registros -> TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet
2025-06-20 20:00:54,705 - LogTransaccionesRunner - INFO - Etapa sp_log_trx completada en 0.33s
2025-06-20 20:00:54,705 - LogTransaccionesRunner - INFO - Ejecutando query: extract_final_csv
2025-06-20 20:00:54,705 - LogTransaccionesRunner - INFO - Variables para extract_final_csv: temp_dir=TEMP_LOGS_TRANSACCIONES, date_folder=********, output_dir_csv=output
2025-06-20 20:00:54,705 - LogTransaccionesRunner - INFO - Query original: -- ========================================================================
-- EXTRACT_FINAL_CSV - Migrado del pipeline_log_transacciones_duckdb.py
-- Extrae el CSV final TR-YYYYMMDD.csv
-- Replica exactamente la query LOG-TRANSACCIONES.sql
-- ========================================================================
-- CODE NINJA MASTER: Query exacta como LOG-TRANSACCIONES.sql

SELECT *
FROM read_parquet('{temp_dir}/{date_folder}/LOG_TRX_FINAL.parquet', union_by_name=true)
WHERE CAST("DateTime" AS DATE) = CAST('{fecha_inicio}' AS DATE)
ORDER BY "DateTime", "TransactionID"

2025-06-20 20:00:54,705 - LogTransaccionesRunner - INFO - Query formateado: -- ========================================================================
-- EXTRACT_FINAL_CSV - Migrado del pipeline_log_transacciones_duckdb.py
-- Extrae el CSV final TR-YYYYMMDD.csv
-- Replica exactamente la query LOG-TRANSACCIONES.sql
-- ========================================================================
-- CODE NINJA MASTER: Query exacta como LOG-TRANSACCIONES.sql

SELECT *
FROM read_parquet('TEMP_LOGS_TRANSACCIONES/********/LOG_TRX_FINAL.parquet', union_by_name=true)
WHERE CAST("DateTime" AS DATE) = CAST('2025-06-19' AS DATE)
ORDER BY "DateTime", "TransactionID"

2025-06-20 20:00:55,259 - LogTransaccionesRunner - INFO - Extract final CSV completado: 219812 registros -> output/TR-********.csv
2025-06-20 20:00:55,259 - LogTransaccionesRunner - INFO - Etapa extract_final_csv completada en 0.55s
2025-06-20 20:00:55,259 - LogTransaccionesRunner - INFO - Iniciando post-procesamiento...
2025-06-20 20:00:55,259 - LogTransaccionesRunner - INFO - Iniciando post-procesamiento LOG_TRANSACCIONES para fecha: 2025-06-19
2025-06-20 20:00:55,259 - LogTransaccionesRunner - INFO - Generando CSV final...
2025-06-20 20:00:55,261 - LogTransaccionesRunner - ERROR - Error en post-procesamiento LOG_TRANSACCIONES: Binder Error: Referenced column "TransferID" not found in FROM clause!
Candidate bindings: "TransactionID", "RealUser", "ToProfile", "TransactionType", "FromID"

LINE 6:             ORDER BY "DateTime", "TransferID"
                                         ^
2025-06-20 20:00:55,261 - LogTransaccionesRunner - ERROR - Error en post-procesamiento: Binder Error: Referenced column "TransferID" not found in FROM clause!
Candidate bindings: "TransactionID", "RealUser", "ToProfile", "TransactionType", "FromID"

LINE 6:             ORDER BY "DateTime", "TransferID"
                                         ^
2025-06-20 20:00:55,261 - LogTransaccionesRunner - INFO - ✅ Pipeline LOG_TRANSACCIONES completado en 2.38s
2025-06-20 20:00:55,261 - LogTransaccionesRunner - INFO - 📁 Archivos generados: 2
2025-06-20 20:00:55,261 - LogTransaccionesRunner - WARNING - ⚠️  Se encontraron 1 errores
