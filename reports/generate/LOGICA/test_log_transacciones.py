#!/usr/bin/env python3
"""
Script de prueba para REPORTE_LOG_TRANSACCIONES
Verifica que la migración al framework LOGICA funcione correctamente
"""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta

def test_configuration():
    """Prueba que la configuración esté correcta"""
    print("🔍 Verificando configuración...")
    
    config_path = Path("CONFIGURACIONES/REPORTE_LOG_TRANSACCIONES/config.ini")
    if not config_path.exists():
        print(f"❌ Error: Archivo de configuración no encontrado: {config_path}")
        return False
    
    print(f"✅ Configuración encontrada: {config_path}")
    return True

def test_queries():
    """Prueba que todos los queries existan"""
    print("🔍 Verificando queries...")
    
    queries_dir = Path("REPORTES/REPORTE_LOG_TRANSACCIONES/queries")
    if not queries_dir.exists():
        print(f"❌ Error: Directorio de queries no encontrado: {queries_dir}")
        return False
    
    required_queries = [
        'sp_pre_log_trx.sql',
        'sp_pre_log_trx_update.sql', 
        'sp_log_trx.sql',
        'extract_final_csv.sql'
    ]
    
    missing_queries = []
    for query_file in required_queries:
        query_path = queries_dir / query_file
        if not query_path.exists():
            missing_queries.append(query_file)
        else:
            print(f"✅ Query encontrado: {query_file}")
    
    if missing_queries:
        print(f"❌ Error: Queries faltantes: {missing_queries}")
        return False
    
    return True

def test_post_processing():
    """Prueba que el script de post-procesamiento exista"""
    print("🔍 Verificando post-procesamiento...")
    
    post_process_path = Path("REPORTES/REPORTE_LOG_TRANSACCIONES/post_process.py")
    if not post_process_path.exists():
        print(f"❌ Error: Script de post-procesamiento no encontrado: {post_process_path}")
        return False
    
    print(f"✅ Post-procesamiento encontrado: {post_process_path}")
    return True

def test_runner():
    """Prueba que el runner personalizado exista"""
    print("🔍 Verificando runner personalizado...")
    
    runner_path = Path("REPORTES/REPORTE_LOG_TRANSACCIONES/run_log_transacciones.py")
    if not runner_path.exists():
        print(f"❌ Error: Runner personalizado no encontrado: {runner_path}")
        return False
    
    print(f"✅ Runner personalizado encontrado: {runner_path}")
    return True

def test_import():
    """Prueba que se puedan importar los módulos"""
    print("🔍 Verificando imports...")
    
    try:
        # Agregar el directorio actual al path
        sys.path.insert(0, str(Path.cwd()))
        
        from REPORTES.REPORTE_LOG_TRANSACCIONES.run_log_transacciones import LogTransaccionesRunner
        print("✅ Import de LogTransaccionesRunner exitoso")
        
        from REPORTES.REPORTE_LOG_TRANSACCIONES.post_process import post_process_log_transacciones
        print("✅ Import de post_process_log_transacciones exitoso")
        
        return True
        
    except ImportError as e:
        print(f"❌ Error de import: {e}")
        return False
    except Exception as e:
        print(f"❌ Error inesperado: {e}")
        return False

def test_query_syntax():
    """Prueba básica de sintaxis de queries"""
    print("🔍 Verificando sintaxis de queries...")
    
    queries_dir = Path("REPORTES/REPORTE_LOG_TRANSACCIONES/queries")
    
    for query_file in queries_dir.glob("*.sql"):
        try:
            with open(query_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Verificaciones básicas
            if not content.strip():
                print(f"⚠️  Advertencia: Query vacío: {query_file.name}")
                continue
            
            # Verificar que tenga placeholders esperados
            if query_file.name == 'sp_pre_log_trx.sql':
                required_placeholders = [
                    '{mtx_transaction_header_path}',
                    '{fecha_inicio}',
                    '{special_users_tuple}'
                ]
                
                for placeholder in required_placeholders:
                    if placeholder not in content:
                        print(f"⚠️  Advertencia: Placeholder faltante en {query_file.name}: {placeholder}")
            
            print(f"✅ Sintaxis básica OK: {query_file.name}")
            
        except Exception as e:
            print(f"❌ Error leyendo {query_file.name}: {e}")
            return False
    
    return True

def run_dry_test():
    """Ejecuta una prueba en seco (sin datos reales)"""
    print("🔍 Ejecutando prueba en seco...")
    
    try:
        # Agregar el directorio actual al path
        sys.path.insert(0, str(Path.cwd()))
        
        from REPORTES.REPORTE_LOG_TRANSACCIONES.run_log_transacciones import LogTransaccionesRunner
        
        config_path = "CONFIGURACIONES/REPORTE_LOG_TRANSACCIONES/config.ini"
        
        # Crear runner (esto debería funcionar sin errores)
        runner = LogTransaccionesRunner(config_path)
        print("✅ Runner creado exitosamente")
        
        # Probar construcción de rutas S3
        fecha_test = "2025-06-20"
        s3_paths = runner.build_s3_paths(fecha_test)
        
        if s3_paths:
            print(f"✅ Rutas S3 construidas: {len(s3_paths)} fuentes")
            
            # Mostrar algunas rutas como ejemplo
            for key, value in list(s3_paths.items())[:3]:
                print(f"   - {key}: {value}")
        else:
            print("⚠️  Advertencia: No se construyeron rutas S3")
        
        # Probar preparación de variables
        variables = runner.prepare_query_variables(fecha_test, s3_paths)
        
        if variables:
            print(f"✅ Variables preparadas: {len(variables)} variables")
        else:
            print("⚠️  Advertencia: No se prepararon variables")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en prueba en seco: {e}")
        return False

def main():
    """Función principal de pruebas"""
    print("🚀 Iniciando pruebas de REPORTE_LOG_TRANSACCIONES")
    print("=" * 60)
    
    # Cambiar al directorio LOGICA
    logica_dir = Path(__file__).parent
    os.chdir(logica_dir)
    print(f"📁 Directorio de trabajo: {logica_dir}")
    
    tests = [
        ("Configuración", test_configuration),
        ("Queries", test_queries),
        ("Post-procesamiento", test_post_processing),
        ("Runner personalizado", test_runner),
        ("Imports", test_import),
        ("Sintaxis de queries", test_query_syntax),
        ("Prueba en seco", run_dry_test)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n🧪 Ejecutando prueba: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ {test_name}: PASÓ")
                passed += 1
            else:
                print(f"❌ {test_name}: FALLÓ")
                failed += 1
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print("📊 RESUMEN DE PRUEBAS")
    print("=" * 60)
    print(f"✅ Pruebas pasadas: {passed}")
    print(f"❌ Pruebas fallidas: {failed}")
    print(f"📈 Tasa de éxito: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 ¡Todas las pruebas pasaron! El sistema está listo.")
        print("\n📋 Próximos pasos:")
        print("1. Ejecutar con datos reales:")
        print("   python run_reporte.py REPORTE_LOG_TRANSACCIONES 2025-06-20")
        print("2. O usar el runner personalizado:")
        print("   cd REPORTES/REPORTE_LOG_TRANSACCIONES")
        print("   python run_log_transacciones.py 2025-06-20")
    else:
        print(f"\n⚠️  Se encontraron {failed} problemas. Revisar antes de ejecutar.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
