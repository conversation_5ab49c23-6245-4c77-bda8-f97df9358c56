#!/usr/bin/env python3
"""
HOMOLOGACIÓN DETALLADA - ETAPA 4
Análisis columna por columna para verificar homologación perfecta
"""

import pandas as pd
import sys

def homologar_columna_por_columna(archivo_original, archivo_logica):
    """
    Realiza homologación detallada columna por columna
    """
    print("🔍 ETAPA 4: HOMOLOGACIÓN DETALLADA COLUMNA POR COLUMNA")
    print("=" * 80)
    
    # Cargar archivos
    print("📂 Cargando archivos...")
    df_original = pd.read_csv(archivo_original)
    df_logica = pd.read_csv(archivo_logica)
    
    print(f"Original: {len(df_original):,} registros")
    print(f"LOGICA: {len(df_logica):,} registros")
    print()
    
    # Verificar columnas
    columnas_original = set(df_original.columns)
    columnas_logica = set(df_logica.columns)
    
    if columnas_original != columnas_logica:
        print("❌ COLUMNAS DIFERENTES")
        print(f"Solo en original: {columnas_original - columnas_logica}")
        print(f"Solo en LOGICA: {columnas_logica - columnas_original}")
        return False
    
    print("✅ COLUMNAS IDÉNTICAS")
    print()
    
    # Análisis por columna
    columnas = list(df_original.columns)
    resultados = []
    
    for i, columna in enumerate(columnas, 1):
        print(f"📊 [{i:2d}/32] Analizando columna: {columna}")
        
        # Valores únicos
        valores_orig = set(df_original[columna].fillna('NULL'))
        valores_logica = set(df_logica[columna].fillna('NULL'))
        
        # Conteos de nulos
        nulos_orig = df_original[columna].isna().sum()
        nulos_logica = df_logica[columna].isna().sum()
        
        # Verificar si son idénticos
        son_identicos = (valores_orig == valores_logica and nulos_orig == nulos_logica)
        
        resultado = {
            'columna': columna,
            'valores_unicos_orig': len(valores_orig),
            'valores_unicos_logica': len(valores_logica),
            'nulos_orig': nulos_orig,
            'nulos_logica': nulos_logica,
            'identicos': son_identicos
        }
        
        resultados.append(resultado)
        
        # Mostrar resultado
        status = "✅" if son_identicos else "❌"
        print(f"   {status} Únicos: {len(valores_orig)} vs {len(valores_logica)} | Nulos: {nulos_orig} vs {nulos_logica}")
        
        if not son_identicos:
            # Mostrar diferencias
            solo_orig = valores_orig - valores_logica
            solo_logica = valores_logica - valores_orig
            
            if solo_orig:
                print(f"   🔍 Solo en original: {list(solo_orig)[:5]}...")
            if solo_logica:
                print(f"   🔍 Solo en LOGICA: {list(solo_logica)[:5]}...")
        
        print()
    
    # Resumen final
    print("=" * 80)
    print("📋 RESUMEN FINAL DE HOMOLOGACIÓN")
    print("=" * 80)
    
    columnas_identicas = sum(1 for r in resultados if r['identicos'])
    columnas_diferentes = len(resultados) - columnas_identicas
    
    print(f"✅ Columnas idénticas: {columnas_identicas}/32")
    print(f"❌ Columnas diferentes: {columnas_diferentes}/32")
    print(f"📊 Porcentaje de homologación: {(columnas_identicas/32)*100:.1f}%")
    
    if columnas_diferentes > 0:
        print("\n🔍 COLUMNAS CON DIFERENCIAS:")
        for r in resultados:
            if not r['identicos']:
                print(f"   ❌ {r['columna']}: {r['valores_unicos_orig']} vs {r['valores_unicos_logica']} únicos, {r['nulos_orig']} vs {r['nulos_logica']} nulos")
    
    return columnas_diferentes == 0

if __name__ == "__main__":
    archivo_original = "/home/<USER>/REPORTE_2025/reports/generate/S3_LOG_TRANSACCIONES/output/TR-20250619.csv"
    archivo_logica = "output/TR-20250619.csv"
    
    homologacion_perfecta = homologar_columna_por_columna(archivo_original, archivo_logica)
    
    if homologacion_perfecta:
        print("\n🎉 ¡HOMOLOGACIÓN PERFECTA! COMO DOS GOTAS DE AGUA 🎉")
        sys.exit(0)
    else:
        print("\n⚠️  HOMOLOGACIÓN PARCIAL - REQUIERE AJUSTES")
        sys.exit(1)
